"""
核心模块（简化版）

包含因子研究框架的核心组件:
- DataAdapter: 数据适配器
- FactorEngine: 因子计算引擎
- FactorAnalyzer: 因子分析引擎
- FactorVisualizer: 结果可视化
"""

from .data_adapter import DataAdapter
from .factor_engine import FactorEngine
from .factor_analyzer import FactorAnalyzer
from .visualizer import FactorVisualizer
# FactorResearch已移除，使用FactorEngine和FactorAnalyzer

__all__ = [
    'DataAdapter',
    'FactorEngine',
    'FactorAnalyzer',
    'FactorVisualizer'
]
