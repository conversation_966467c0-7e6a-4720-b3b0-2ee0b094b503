"""
统一数据处理工具

提供项目中通用的数据处理函数，减少代码重复。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Tuple
import re
import logging

logger = logging.getLogger(__name__)


class DataUtils:
    """数据处理工具类"""
    
    # 股票代码正则表达式
    STOCK_CODE_PATTERNS = {
        'A_STOCK': r'^[0-9]{6}$',           # A股6位数字
        'A_STOCK_WITH_SUFFIX': r'^[0-9]{6}\.(SZ|SH)$',  # 带后缀的A股
        'HK_STOCK': r'^[0-9]{5}\.HK$',      # 港股
        'US_STOCK': r'^[A-Z]{1,5}$'         # 美股
    }
    
    # 字段映射配置
    FIELD_MAPPINGS = {
        'price_fields': {
            'open': ['open', 'Open', 'OPEN', 'opening_price'],
            'high': ['high', 'High', 'HIGH', 'highest_price'],
            'low': ['low', 'Low', 'LOW', 'lowest_price'],
            'close': ['close', 'Close', 'CLOSE', 'closing_price'],
            'volume': ['volume', 'Volume', 'VOLUME', 'vol', 'Vol'],
            'amount': ['amount', 'Amount', 'AMOUNT', 'turnover', 'value']
        },
        'date_fields': {
            'date': ['date', 'Date', 'DATE', 'trade_date', 'trading_date', 'datetime']
        },
        'code_fields': {
            'code': ['code', 'Code', 'CODE', 'symbol', 'Symbol', 'SYMBOL', 'stock_code', 'asset']
        }
    }
    
    @staticmethod
    def standardize_stock_code(code: str, target_format: str = 'simple') -> str:
        """
        标准化股票代码
        
        Parameters:
        -----------
        code : str
            原始股票代码
        target_format : str
            目标格式 ('simple', 'with_suffix', 'exchange_prefix')
            
        Returns:
        --------
        str
            标准化后的股票代码
        """
        if not isinstance(code, str):
            return str(code)
        
        # 清理代码
        code = code.strip().upper()
        
        # 移除常见前缀和后缀
        code = re.sub(r'^(SZ|SH|HK)\.?', '', code)
        code = re.sub(r'\.(SZ|SH|HK)$', '', code)
        
        # 确保A股代码为6位
        if re.match(r'^[0-9]{1,6}$', code):
            code = code.zfill(6)
        
        # 根据目标格式处理
        if target_format == 'simple':
            return code
        elif target_format == 'with_suffix':
            if code.startswith(('000', '002', '300')):
                return f"{code}.SZ"
            elif code.startswith(('600', '601', '603', '688')):
                return f"{code}.SH"
            else:
                return code
        elif target_format == 'exchange_prefix':
            if code.startswith(('000', '002', '300')):
                return f"SZ.{code}"
            elif code.startswith(('600', '601', '603', '688')):
                return f"SH.{code}"
            else:
                return code
        
        return code
    
    @staticmethod
    def validate_stock_code(code: str) -> bool:
        """
        验证股票代码格式
        
        Parameters:
        -----------
        code : str
            股票代码
            
        Returns:
        --------
        bool
            是否为有效的股票代码
        """
        if not isinstance(code, str):
            return False
        
        code = code.strip()
        
        # 检查各种格式
        for pattern in DataUtils.STOCK_CODE_PATTERNS.values():
            if re.match(pattern, code):
                return True
        
        return False
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> Tuple[bool, str]:
        """
        验证日期范围
        
        Parameters:
        -----------
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns:
        --------
        Tuple[bool, str]
            (是否有效, 错误信息)
        """
        try:
            start = pd.to_datetime(start_date)
            end = pd.to_datetime(end_date)
            
            if start > end:
                return False, "开始日期不能晚于结束日期"
            
            if start > pd.Timestamp.now():
                return False, "开始日期不能是未来日期"
            
            # 检查日期范围是否合理（不超过10年）
            if (end - start).days > 10 * 365:
                return False, "日期范围不能超过10年"
            
            return True, ""
            
        except Exception as e:
            return False, f"日期格式错误: {str(e)}"
    
    @staticmethod
    def standardize_date_format(date_input: Union[str, datetime, pd.Timestamp]) -> str:
        """
        标准化日期格式为 YYYY-MM-DD
        
        Parameters:
        -----------
        date_input : Union[str, datetime, pd.Timestamp]
            输入日期
            
        Returns:
        --------
        str
            标准化的日期字符串
        """
        try:
            if isinstance(date_input, str):
                # 处理常见的日期格式
                date_input = date_input.replace('/', '-').replace('.', '-')
                dt = pd.to_datetime(date_input)
            else:
                dt = pd.to_datetime(date_input)
            
            return dt.strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.error(f"Date standardization failed for {date_input}: {e}")
            return str(date_input)
    
    @staticmethod
    def apply_field_mapping(df: pd.DataFrame, mapping_type: str = 'price_fields') -> pd.DataFrame:
        """
        应用字段映射，统一字段名称
        
        Parameters:
        -----------
        df : pd.DataFrame
            输入数据框
        mapping_type : str
            映射类型
            
        Returns:
        --------
        pd.DataFrame
            映射后的数据框
        """
        if df.empty:
            return df
        
        df_mapped = df.copy()
        
        # 获取映射配置
        if mapping_type not in DataUtils.FIELD_MAPPINGS:
            logger.warning(f"Unknown mapping type: {mapping_type}")
            return df_mapped
        
        mappings = DataUtils.FIELD_MAPPINGS[mapping_type]
        
        # 应用映射
        for standard_field, possible_fields in mappings.items():
            for field in possible_fields:
                if field in df_mapped.columns and standard_field not in df_mapped.columns:
                    df_mapped = df_mapped.rename(columns={field: standard_field})
                    logger.debug(f"Mapped field: {field} -> {standard_field}")
                    break
        
        return df_mapped
    
    @staticmethod
    def clean_numeric_data(df: pd.DataFrame, numeric_columns: List[str] = None) -> pd.DataFrame:
        """
        清理数值数据
        
        Parameters:
        -----------
        df : pd.DataFrame
            输入数据框
        numeric_columns : List[str], optional
            数值列名列表，None时自动检测
            
        Returns:
        --------
        pd.DataFrame
            清理后的数据框
        """
        if df.empty:
            return df
        
        df_clean = df.copy()
        
        if numeric_columns is None:
            # 自动检测数值列
            numeric_columns = df_clean.select_dtypes(include=[np.number]).columns.tolist()
        
        for col in numeric_columns:
            if col in df_clean.columns:
                # 转换为数值类型
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
                
                # 处理异常值（使用MAD方法）
                median = df_clean[col].median()
                mad = np.median(np.abs(df_clean[col] - median))
                
                if mad > 0:
                    threshold = 3.0  # 3倍MAD
                    outlier_mask = np.abs(df_clean[col] - median) > threshold * mad
                    outlier_count = outlier_mask.sum()
                    
                    if outlier_count > 0:
                        logger.info(f"Found {outlier_count} outliers in column {col}")
                        # 可以选择删除或替换异常值
                        # df_clean.loc[outlier_mask, col] = np.nan
        
        return df_clean
    
    @staticmethod
    def resample_data(df: pd.DataFrame, freq: str, date_column: str = 'date') -> pd.DataFrame:
        """
        重采样数据到指定频率
        
        Parameters:
        -----------
        df : pd.DataFrame
            输入数据框
        freq : str
            目标频率 ('D', 'W', 'M', 'Q', 'Y')
        date_column : str
            日期列名
            
        Returns:
        --------
        pd.DataFrame
            重采样后的数据框
        """
        if df.empty or date_column not in df.columns:
            return df
        
        df_resampled = df.copy()
        
        # 确保日期列为datetime类型
        df_resampled[date_column] = pd.to_datetime(df_resampled[date_column])
        
        # 设置日期为索引
        df_resampled = df_resampled.set_index(date_column)
        
        # 定义聚合规则
        agg_rules = {
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }
        
        # 只对存在的列应用聚合
        available_agg_rules = {k: v for k, v in agg_rules.items() if k in df_resampled.columns}
        
        if available_agg_rules:
            df_resampled = df_resampled.resample(freq).agg(available_agg_rules)
        else:
            df_resampled = df_resampled.resample(freq).last()
        
        # 重置索引
        df_resampled = df_resampled.reset_index()
        
        return df_resampled
    
    @staticmethod
    def calculate_returns(df: pd.DataFrame, price_column: str = 'close', 
                         return_type: str = 'simple') -> pd.Series:
        """
        计算收益率
        
        Parameters:
        -----------
        df : pd.DataFrame
            价格数据
        price_column : str
            价格列名
        return_type : str
            收益率类型 ('simple', 'log')
            
        Returns:
        --------
        pd.Series
            收益率序列
        """
        if df.empty or price_column not in df.columns:
            return pd.Series()
        
        prices = df[price_column]
        
        if return_type == 'simple':
            returns = prices.pct_change()
        elif return_type == 'log':
            returns = np.log(prices / prices.shift(1))
        else:
            raise ValueError(f"Unknown return type: {return_type}")
        
        return returns
    
    @staticmethod
    def detect_data_quality_issues(df: pd.DataFrame) -> Dict[str, Any]:
        """
        检测数据质量问题
        
        Parameters:
        -----------
        df : pd.DataFrame
            输入数据框
            
        Returns:
        --------
        Dict[str, Any]
            数据质量报告
        """
        if df.empty:
            return {'status': 'empty_dataframe'}
        
        report = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'missing_data': {},
            'duplicate_rows': 0,
            'data_types': {},
            'outliers': {},
            'recommendations': []
        }
        
        # 检查缺失值
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            missing_pct = missing_count / len(df) * 100
            
            if missing_count > 0:
                report['missing_data'][col] = {
                    'count': int(missing_count),
                    'percentage': round(missing_pct, 2)
                }
                
                if missing_pct > 50:
                    report['recommendations'].append(f"列 {col} 缺失值过多 ({missing_pct:.1f}%)，建议检查数据源")
        
        # 检查重复行
        report['duplicate_rows'] = int(df.duplicated().sum())
        if report['duplicate_rows'] > 0:
            report['recommendations'].append(f"发现 {report['duplicate_rows']} 行重复数据")
        
        # 检查数据类型
        for col in df.columns:
            report['data_types'][col] = str(df[col].dtype)
        
        # 检查数值列的异常值
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            
            if IQR > 0:
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                outlier_count = len(outliers)
                
                if outlier_count > 0:
                    report['outliers'][col] = {
                        'count': outlier_count,
                        'percentage': round(outlier_count / len(df) * 100, 2),
                        'bounds': [float(lower_bound), float(upper_bound)]
                    }
        
        return report
    
    @staticmethod
    def create_multiindex_dataframe(data: List[Dict], 
                                   index_columns: List[str] = ['date', 'asset']) -> pd.DataFrame:
        """
        创建MultiIndex数据框
        
        Parameters:
        -----------
        data : List[Dict]
            数据列表
        index_columns : List[str]
            索引列名
            
        Returns:
        --------
        pd.DataFrame
            MultiIndex数据框
        """
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        
        # 检查索引列是否存在
        missing_columns = [col for col in index_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing index columns: {missing_columns}")
            return df
        
        # 设置MultiIndex
        df = df.set_index(index_columns)
        
        return df
