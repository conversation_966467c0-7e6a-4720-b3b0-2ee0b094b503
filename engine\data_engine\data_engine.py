import akshare as ak
import pandas as pd
import logging
from typing import List
from .data_cleaner import data_cleaner

logger = logging.getLogger(__name__)

class DataEngine(object):
    ETF_PREFIXES = ("5", "1", "15", "16", "51")

    def __init__(self, quant_db=None):
        self.quant_db = quant_db

    def _convert_name_em(self, df) -> pd.DataFrame:
        df_new = df.rename(columns={
            '序号': 'index',
            '代码': 'code',
            '名称': 'name',
            '最新价': 'latest_price',
            '今开': 'open',
            '昨收': 'previous_close',
            '量比': 'volume_ratio',
            '日期': 'date',
            '数据日期': 'date',
            '更新时间': 'update_time',
            '开盘': 'open',
            '开盘价': 'open',
            '最高价': 'high',
            '最高': 'high',
            '最低价': 'low',
            '最低': 'low',
            '收盘': 'close',
            '涨跌幅': 'change',
            '涨跌额': 'change_amount',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '换手率': 'turnover_rate',
            '市盈率-动态': 'pe_dynamic',
            '市净率': 'pb',
            '市盈率-静态': 'pe_static',
            '总市值': 'market_cap',
            '涨速': 'speed',
            
        })
        return df_new

    def _get_stock_spot_info(self):
        stock_info = ak.stock_zh_a_spot_em()
        return stock_info
    
    def _get_etf_spot_info(self):
        etf_info = ak.fund_etf_spot_em()
        return etf_info

    @staticmethod
    def is_etf_code(code):
        return code.startswith(DataEngine.ETF_PREFIXES)

    def _get_stock_spot_info_by_code(self, code):
        # 使用统一ETF判断
        if self.is_etf_code(code):
            # 取ETF行情
            df = ak.fund_etf_spot_em()
        else:
            # 取股票行情
            df = ak.stock_zh_a_spot_em()
        
        row = df[df['代码'] == code]
        if not row.empty:
            name = row.iloc[0]['名称']
            price = row.iloc[0]['最新价']
            return {'name': name, 'price': price}
        return {'name': '', 'price': ''}

    def _get_stock_spot_info_pool(self, stock_pool):
        """
        获取股票池(stock_pool为代码列表)的现价和名称，返回列表[dict]，每个dict为该标的的完整一行数据（akshare原始数据，dict格式）。
        """
        stock_info = ak.stock_zh_a_spot_em()
        etf_info = ak.fund_etf_spot_em()
        if stock_info is None or etf_info is None:
            return []
        result = []
        for code in stock_pool:
            if self.is_etf_code(code):
                df = etf_info
            else:
                df = stock_info
            
            df = self._convert_name_em(df)
            row = df[df['code'] == code]            
            if not row.empty:
                # 返回该行所有字段的dict
                result.append(row.iloc[0].to_dict())
            else:
                result.append({'code': code})  # 只返回代码，其他字段缺失
        return result

    def _get_stock_hist_info(self, symbol, period, adjust, file_path=None):    
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if(symbol.startswith("H")):
                    symbol = symbol[1:]
                    df = ak.stock_hk_hist(symbol, period, adjust)
                else:
                    if self.is_etf_code(symbol):
                        df = ak.fund_etf_hist_em(symbol, period, adjust)
                    else:
                        df = ak.stock_zh_a_hist(symbol, period, adjust)
                df = self._convert_name_em(df)
                if file_path != None:
                    df.to_csv(file_path, index=False, encoding='utf_8_sig')
                return df
            except Exception as e:
                if attempt < max_retries - 1:
                    import time
                    time.sleep(2)
                    continue
                else:
                    raise ValueError(f"获取股票数据失败: {str(e)}") from e

    def _get_option_hist_info(self, symbol):
        pass

    def _get_option_hist_info_for_year(self, year):
        pass


    def _convert_spot_to_hist_em(self, spot_df):
        """
        将现货数据转换为历史数据格式，返回DataFrame。
        """
        spot_df = self._convert_name_em(spot_df)
        # 只保留需要的列
        columns = ['date', 'open', 'latest_price', 'high', 'low', 'volume', 'amount', 'amplitude', 'change', 'change_amount', 'turnover_rate']
        hist_df = spot_df[columns].copy()
        hist_df['date'] = pd.to_datetime(hist_df['date'])  # 保持为datetime类型
        hist_df.rename(columns={'latest_price': 'close'}, inplace=True)
        hist_df = hist_df.set_index('date')  # 设置index为date
        return hist_df

    def _replace_last_bar(self, datafeed, latest_bar):
        """
        替换数据源的最后一行数据，latest_bar为akshare最新行情dict，需按历史数据格式写入。
        """
        # 利用_convert_spot_to_hist_em生成标准格式DataFrame
        spot_df = pd.DataFrame([latest_bar])
        hist_df = self._convert_spot_to_hist_em(spot_df)
        if hasattr(datafeed, 'p') and hasattr(datafeed.p, 'dataname'):
            df = datafeed.p.dataname
            if hasattr(df, 'iloc') and len(df) > 0 and len(hist_df) > 0:
                # 对齐列名，确保赋值不会出错
                hist_row = hist_df.iloc[0]
                hist_row = hist_row.reindex(df.columns)  # 按df的列顺序对齐
                df.loc[df.index[-1], :] = hist_row.values
                # 确保 index 还是 DatetimeIndex
                if not isinstance(df.index, pd.DatetimeIndex):
                    df.index = pd.to_datetime(df.index)
                datafeed.p.dataname = df  # 更新回去

    def _append_last_bar(self, datafeed, latest_bar):
        """
        向数据源追加最新一行数据，latest_bar为akshare最新行情dict，需按历史数据格式写入。
        """
        spot_df = pd.DataFrame([latest_bar])
        hist_df = self._convert_spot_to_hist_em(spot_df)
        if hasattr(datafeed, 'p') and hasattr(datafeed.p, 'dataname'):
            df = datafeed.p.dataname
            if hasattr(df, 'append') and len(hist_df) > 0:
                df = df.append(hist_df.iloc[0], ignore_index=True)
                datafeed.p.dataname = df  # 更新回去

    @staticmethod
    def get_category_by_code(code):
        """
        根据标的代码判断数据库category（'stock' 或 'etf'）
        """
        return "etf" if DataEngine.is_etf_code(code) else "stock"

    def download_and_clean_data(self, symbol: str, start_date: str, end_date: str,
                               period: str = "daily", adjust: str = "qfq") -> pd.DataFrame:
        """
        下载数据并立即进行清洗处理

        Parameters:
        -----------
        symbol : str
            股票代码
        start_date : str
            开始日期
        end_date : str
            结束日期
        period : str
            数据周期，默认daily
        adjust : str
            复权类型，默认前复权

        Returns:
        --------
        pd.DataFrame
            清洗后的数据，已设置标准索引和时区
        """
        try:
            logger.info(f"Downloading and cleaning data for {symbol} from {start_date} to {end_date}")

            # 1. 下载原始数据
            raw_data = ak.stock_zh_a_hist(
                symbol=symbol,
                period=period,
                start_date=start_date.replace('-', ''),
                end_date=end_date.replace('-', ''),
                adjust=adjust
            )

            if raw_data.empty:
                logger.warning(f"No data downloaded for {symbol}")
                return pd.DataFrame()

            # 2. 添加资产代码
            raw_data['code'] = symbol

            logger.debug(f"Downloaded {len(raw_data)} records for {symbol}")

            # 3. 立即进行数据清洗
            cleaned_data = data_cleaner.clean_stock_data(raw_data, data_source="akshare")

            logger.info(f"Data cleaning completed for {symbol}: {len(cleaned_data)} clean records")

            return cleaned_data

        except Exception as e:
            logger.error(f"Error downloading and cleaning data for {symbol}: {e}")
            return pd.DataFrame()

    def batch_download_and_clean(self, symbols: list, start_date: str, end_date: str,
                                period: str = "daily", adjust: str = "qfq") -> pd.DataFrame:
        """
        批量下载和清洗数据

        Parameters:
        -----------
        symbols : list
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        period : str
            数据周期
        adjust : str
            复权类型

        Returns:
        --------
        pd.DataFrame
            合并后的清洗数据
        """
        try:
            logger.info(f"Batch downloading and cleaning data for {len(symbols)} symbols")

            all_data = []

            for symbol in symbols:
                try:
                    cleaned_data = self.download_and_clean_data(
                        symbol=symbol,
                        start_date=start_date,
                        end_date=end_date,
                        period=period,
                        adjust=adjust
                    )

                    if not cleaned_data.empty:
                        all_data.append(cleaned_data)

                except Exception as e:
                    logger.error(f"Error processing {symbol}: {e}")
                    continue

            if all_data:
                # 合并所有数据
                combined_data = pd.concat(all_data, ignore_index=False)
                logger.info(f"Batch processing completed: {len(combined_data)} total records")
                return combined_data
            else:
                logger.warning("No data successfully processed in batch")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error in batch download and clean: {e}")
            return pd.DataFrame()

    def save_cleaned_data_to_db(self, cleaned_data: pd.DataFrame, table_name: str = None):
        """
        将清洗后的数据保存到数据库

        Parameters:
        -----------
        cleaned_data : pd.DataFrame
            清洗后的数据
        table_name : str, optional
            表名，如果不指定则自动生成
        """
        try:
            if cleaned_data.empty:
                logger.warning("No data to save to database")
                return

            if self.quant_db is None:
                logger.warning("No database connection available")
                return

            # 自动生成表名
            if table_name is None:
                table_name = "stock_daily_cleaned"

            # 保存到数据库
            # 注意：这里需要根据实际的数据库接口调整
            logger.info(f"Saving {len(cleaned_data)} cleaned records to database table: {table_name}")

            # 示例保存逻辑（需要根据实际数据库接口调整）
            # self.quant_db.save_data(cleaned_data, table_name)

            logger.info("Data saved to database successfully")

        except Exception as e:
            logger.error(f"Error saving cleaned data to database: {e}")

    def get_cleaning_stats(self) -> dict:
        """获取数据清洗统计信息"""
        return data_cleaner.get_cleaning_stats()

    def get_factor_ready_price_data(self, assets: List[str], start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取因子分析就绪的价格数据

        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期

        Returns:
        --------
        pd.DataFrame
            因子分析就绪的价格数据，已完成所有清洗和格式化
        """
        try:
            logger.info(f"Getting factor-ready price data for {len(assets)} assets")

            # 1. 获取原始价格数据（这里需要根据实际数据库接口调整）
            # 示例：从数据库获取价格数据
            raw_price_data = self._get_raw_price_data_from_db(assets, start_date, end_date)

            if raw_price_data.empty:
                logger.warning("No raw price data found")
                return pd.DataFrame()

            # 2. 使用data_cleaner进行因子分析专用清洗
            cleaned_price_data = data_cleaner.clean_price_data_for_analysis(
                raw_price_data,
                data_source="database"
            )

            logger.info(f"Factor-ready price data prepared: {cleaned_price_data.shape}")
            return cleaned_price_data

        except Exception as e:
            logger.error(f"Error getting factor-ready price data: {e}")
            raise

    def prepare_factor_data_for_analysis(self, factor_data: pd.Series) -> pd.Series:
        """
        准备因子数据用于分析

        Parameters:
        -----------
        factor_data : pd.Series
            原始因子数据

        Returns:
        --------
        pd.Series
            因子分析就绪的因子数据，已完成所有清洗和格式化
        """
        try:
            logger.info("Preparing factor data for analysis")

            # 使用data_cleaner进行因子分析专用清洗
            cleaned_factor_data = data_cleaner.clean_factor_data_for_analysis(
                factor_data,
                data_source="factor_calculation"
            )

            logger.info(f"Factor data prepared for analysis: {cleaned_factor_data.shape}")
            return cleaned_factor_data

        except Exception as e:
            logger.error(f"Error preparing factor data for analysis: {e}")
            raise

    def _get_raw_price_data_from_db(self, assets: List[str], start_date: str, end_date: str) -> pd.DataFrame:
        """从数据库获取原始价格数据"""
        try:
            logger.debug(f"Fetching raw price data for {len(assets)} assets from database")

            # 如果有数据库连接，直接查询
            if self.quant_db:
                # 首先检查表是否存在
                try:
                    tables_result = self.quant_db.conn.execute(
                        "SELECT name FROM sqlite_master WHERE type='table'"
                    ).fetchall()
                    available_tables = [row[0] for row in tables_result]

                    # 确定要使用的表
                    table_candidates = ['stock_daily', 'stock_1d', 'stock_day']
                    table_to_use = None

                    for table in table_candidates:
                        if table in available_tables:
                            table_to_use = table
                            break

                    if not table_to_use:
                        logger.warning("No suitable stock data table found")
                        return pd.DataFrame()

                    # 构建查询
                    assets_str = "', '".join(assets)
                    query = f"""
                    SELECT date, code, open, high, low, close, volume, amount
                    FROM {table_to_use}
                    WHERE code IN ('{assets_str}')
                    AND date >= '{start_date}'
                    AND date <= '{end_date}'
                    ORDER BY date, code
                    """

                    df = self.quant_db.conn.execute(query).df()

                    if not df.empty and 'date' in df.columns and 'code' in df.columns:
                        # 转换为MultiIndex格式
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.set_index(['date', 'code'])
                        df.index.names = ['date', 'asset']

                    return df

                except Exception as query_error:
                    logger.error(f"Database query error: {query_error}")
                    return pd.DataFrame()

            # 如果没有数据库连接，返回空DataFrame
            logger.warning("No database connection available")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error fetching raw price data from database: {e}")
            return pd.DataFrame()
