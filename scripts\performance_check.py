#!/usr/bin/env python3
"""
性能检查脚本

快速检查项目的性能状况和代码质量。
提供系统优化建议和性能报告。
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common import (
    get_performance_manager, analyze_code_quality, get_config, get_logger,
    PerformanceMonitor, MemoryManager
)


def check_system_performance():
    """检查系统性能"""
    print("🔍 检查系统性能...")
    
    try:
        # 获取性能管理器
        performance_manager = get_performance_manager()
        
        # 启动监控
        performance_manager.start_monitoring()

        # 等待一段时间收集数据
        print("   收集性能数据中...")
        time.sleep(5)

        # 获取性能报告
        report = performance_manager.get_performance_report()

        # 停止监控
        performance_manager.stop_monitoring()
        
        # 显示结果
        system_stats = report.get('system_stats', {})
        print(f"   CPU使用率: {system_stats.get('current_cpu', 0):.1f}%")
        print(f"   内存使用率: {system_stats.get('current_memory', 0):.1f}%")
        print(f"   内存使用量: {system_stats.get('current_memory_mb', 0):.1f}MB")
        
        # 内存优化
        print("   执行内存优化...")
        memory_result = MemoryManager.optimize_memory()
        collected = memory_result.get('collected_objects', 0)
        print(f"   清理了 {collected} 个对象")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 性能检查失败: {e}")
        return False


def check_code_quality():
    """检查代码质量"""
    print("📊 检查代码质量...")
    
    try:
        # 分析代码质量
        print("   分析代码复杂度和SOLID原则...")
        report = analyze_code_quality(str(project_root))
        
        # 显示结果
        quality_score = report.get('quality_score', 0)
        print(f"   代码质量评分: {quality_score}/100")
        
        # 复杂度分析
        complexity_analysis = report.get('complexity_analysis', {})
        summary = complexity_analysis.get('summary', {})
        
        print(f"   总文件数: {summary.get('total_files', 0)}")
        print(f"   总函数数: {summary.get('total_functions', 0)}")
        print(f"   平均复杂度: {summary.get('avg_complexity_per_function', 0):.1f}")
        
        # 高复杂度函数
        high_complexity = summary.get('high_complexity_functions', [])
        if high_complexity:
            print(f"   高复杂度函数: {len(high_complexity)}个")
            for func in high_complexity[:3]:  # 显示前3个
                print(f"     - {func['function']} (复杂度: {func['complexity']})")
        
        # SOLID违反
        solid_violations = report.get('solid_violations', [])
        if solid_violations:
            print(f"   SOLID原则违反: {len(solid_violations)}个")
            violation_types = {}
            for violation in solid_violations:
                principle = violation['principle']
                violation_types[principle] = violation_types.get(principle, 0) + 1
            
            for principle, count in violation_types.items():
                print(f"     - {principle}: {count}个")
        
        # 改进建议
        recommendations = report.get('recommendations', [])
        if recommendations:
            print("   改进建议:")
            for rec in recommendations[:5]:  # 显示前5个建议
                print(f"     - {rec}")
        
        return quality_score >= 70  # 质量评分阈值
        
    except Exception as e:
        print(f"   ❌ 代码质量检查失败: {e}")
        return False


def check_configuration():
    """检查配置状态"""
    print("⚙️ 检查配置状态...")
    
    try:
        config = get_config()
        
        # 检查数据库配置
        db_dir = Path(config.database.base_dir)
        print(f"   数据库目录: {db_dir}")
        print(f"   目录存在: {'✅' if db_dir.exists() else '❌'}")
        
        # 检查日志配置
        log_path = Path(config.get_log_path())
        print(f"   日志文件: {log_path}")
        print(f"   日志目录存在: {'✅' if log_path.parent.exists() else '❌'}")
        
        # 检查缓存配置
        print(f"   缓存启用: {'✅' if config.data_engine.cache_enabled else '❌'}")
        print(f"   并行处理: {'✅' if config.factor_research.parallel_enabled else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
        return False


def check_dependencies():
    """检查依赖状态"""
    print("📦 检查依赖状态...")
    
    dependencies = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('PyQt5', 'PyQt5'),
        ('duckdb', 'duckdb'),
        ('akshare', 'akshare'),
        ('psutil', 'psutil'),
        ('yaml', 'PyYAML')
    ]
    
    missing_deps = []
    
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"   {dep_name}: ✅")
        except ImportError:
            print(f"   {dep_name}: ❌")
            missing_deps.append(dep_name)
    
    # 检查可选依赖
    optional_deps = [
        ('alphalens', 'alphalens'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn')
    ]
    
    print("   可选依赖:")
    for dep_name, import_name in optional_deps:
        try:
            __import__(import_name)
            print(f"     {dep_name}: ✅")
        except ImportError:
            print(f"     {dep_name}: ⚠️ (可选)")
    
    if missing_deps:
        print(f"   ❌ 缺少必需依赖: {', '.join(missing_deps)}")
        return False
    
    return True


def check_file_structure():
    """检查文件结构"""
    print("📁 检查文件结构...")
    
    required_dirs = [
        'common', 'database', 'engine', 'factor_research',
        'gui', 'portfolio', 'config', 'docs', 'test'
    ]
    
    missing_dirs = []
    
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"   {dir_name}/: ✅")
        else:
            print(f"   {dir_name}/: ❌")
            missing_dirs.append(dir_name)
    
    # 检查关键文件
    key_files = [
        'main.py',
        'common/__init__.py',
        'common/unified_config.py',
        'common/unified_logger.py',
        'common/unified_exceptions.py'
    ]
    
    missing_files = []
    
    for file_name in key_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"   {file_name}: ✅")
        else:
            print(f"   {file_name}: ❌")
            missing_files.append(file_name)
    
    if missing_dirs or missing_files:
        print(f"   ❌ 文件结构不完整")
        return False
    
    return True


def generate_summary(results):
    """生成检查总结"""
    print("\n" + "="*50)
    print("📋 检查总结")
    print("="*50)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"通过率: {passed_checks/total_checks*100:.1f}%")
    
    print("\n检查结果:")
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查都通过了！系统状态良好。")
    elif passed_checks >= total_checks * 0.8:
        print("\n⚠️ 大部分检查通过，但有一些问题需要注意。")
    else:
        print("\n❌ 多项检查失败，建议进行系统维护。")
    
    return passed_checks / total_checks


def main():
    """主函数"""
    print("🚀 T_TRADE 性能检查工具")
    print("="*50)
    
    # 执行各项检查
    results = {}
    
    results["文件结构"] = check_file_structure()
    results["依赖状态"] = check_dependencies()
    results["配置状态"] = check_configuration()
    results["系统性能"] = check_system_performance()
    results["代码质量"] = check_code_quality()
    
    # 生成总结
    success_rate = generate_summary(results)
    
    # 返回退出代码
    return 0 if success_rate >= 0.8 else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ 检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 检查过程中发生错误: {e}")
        sys.exit(1)
