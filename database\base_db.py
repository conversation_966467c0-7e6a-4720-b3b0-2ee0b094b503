"""
数据库基类

提供统一的数据库操作接口，减少重复代码，提高可维护性。
遵循DRY原则，将公共的数据库操作逻辑抽象到基类中。
"""

import os
import pandas as pd
import duckdb
import logging
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod
from datetime import datetime

logger = logging.getLogger(__name__)

# 数据库配置
DATA_BASE_DIR = os.path.join(os.path.dirname(__file__), "..", "data")
if not os.path.exists(DATA_BASE_DIR):
    os.makedirs(DATA_BASE_DIR)


class BaseDB(ABC):
    """
    数据库基类
    
    提供统一的数据库操作接口，包括：
    - 连接管理
    - 表操作
    - 数据查询和插入
    - 事务管理
    - 错误处理
    """
    
    def __init__(self, db_path: str = None, read_only: bool = False):
        """
        初始化数据库连接
        
        Parameters:
        -----------
        db_path : str, optional
            数据库文件路径
        read_only : bool, optional
            是否只读模式，默认False
        """
        self.db_path = db_path or self._get_default_db_path()
        self.read_only = read_only
        self.conn = None
        self._connection_pool = {}
        self._transaction_active = False
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 建立连接
        self._connect()
        
        # 初始化表结构
        self._init_tables()
        
        logger.info(f"Database initialized: {self.db_path}")
    
    @abstractmethod
    def _get_default_db_path(self) -> str:
        """获取默认数据库路径"""
        pass
    
    @abstractmethod
    def _init_tables(self):
        """初始化表结构"""
        pass
    
    def _connect(self):
        """建立数据库连接"""
        try:
            self.conn = duckdb.connect(database=self.db_path, read_only=self.read_only)
            logger.debug(f"Connected to database: {self.db_path}")
        except Exception as e:
            logger.error(f"Failed to connect to database {self.db_path}: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
            logger.debug("Database connection closed")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    # ==================== 表操作方法 ====================
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            result = self.conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table_name,)
            ).fetchone()
            return result is not None
        except Exception as e:
            logger.error(f"Error checking table existence {table_name}: {e}")
            return False
    
    def get_table_columns(self, table_name: str) -> List[str]:
        """获取表的列名"""
        try:
            result = self.conn.execute(f"PRAGMA table_info({table_name})").fetchall()
            return [row[1] for row in result]  # 列名在第二个位置
        except Exception as e:
            logger.error(f"Error getting table columns for {table_name}: {e}")
            return []
    
    def create_table_from_df(self, table_name: str, df: pd.DataFrame, 
                           if_exists: str = "replace") -> bool:
        """
        从DataFrame创建表
        
        Parameters:
        -----------
        table_name : str
            表名
        df : pd.DataFrame
            数据框
        if_exists : str
            如果表存在的处理方式: 'replace', 'append', 'fail'
            
        Returns:
        --------
        bool
            是否成功创建
        """
        try:
            if if_exists == "replace":
                self.conn.execute(f"DROP TABLE IF EXISTS {table_name}")
            elif if_exists == "fail" and self.table_exists(table_name):
                raise ValueError(f"Table {table_name} already exists")
            
            # 注册临时DataFrame
            self.conn.register("tmp_df", df)
            
            # 创建表
            if if_exists == "replace":
                self.conn.execute(f"CREATE TABLE {table_name} AS SELECT * FROM tmp_df")
            else:  # append
                if not self.table_exists(table_name):
                    self.conn.execute(f"CREATE TABLE {table_name} AS SELECT * FROM tmp_df")
                else:
                    self.conn.execute(f"INSERT INTO {table_name} SELECT * FROM tmp_df")
            
            # 清理临时注册
            self.conn.unregister("tmp_df")
            
            logger.debug(f"Table {table_name} created/updated with {len(df)} rows")
            return True
            
        except Exception as e:
            logger.error(f"Error creating table {table_name}: {e}")
            return False
    
    def drop_table(self, table_name: str) -> bool:
        """删除表"""
        try:
            self.conn.execute(f"DROP TABLE IF EXISTS {table_name}")
            logger.debug(f"Table {table_name} dropped")
            return True
        except Exception as e:
            logger.error(f"Error dropping table {table_name}: {e}")
            return False
    
    # ==================== 数据操作方法 ====================
    
    def insert_df(self, table_name: str, df: pd.DataFrame) -> bool:
        """向表中插入DataFrame数据"""
        try:
            if df.empty:
                logger.warning(f"Empty DataFrame provided for table {table_name}")
                return True
            
            self.conn.register("tmp_df", df)
            self.conn.execute(f"INSERT INTO {table_name} SELECT * FROM tmp_df")
            self.conn.unregister("tmp_df")
            
            logger.debug(f"Inserted {len(df)} rows into {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting data into {table_name}: {e}")
            return False
    
    def upsert_df(self, table_name: str, df: pd.DataFrame, 
                  key_columns: List[str]) -> bool:
        """
        更新或插入DataFrame数据
        
        Parameters:
        -----------
        table_name : str
            表名
        df : pd.DataFrame
            数据框
        key_columns : List[str]
            用于判断重复的关键列
            
        Returns:
        --------
        bool
            是否成功
        """
        try:
            if df.empty:
                return True
            
            # 简单实现：先删除再插入
            for _, row in df.iterrows():
                # 构建删除条件
                conditions = []
                for col in key_columns:
                    value = row[col]
                    if pd.isna(value):
                        conditions.append(f"{col} IS NULL")
                    else:
                        conditions.append(f"{col} = '{value}'")
                
                where_clause = " AND ".join(conditions)
                self.conn.execute(f"DELETE FROM {table_name} WHERE {where_clause}")
            
            # 插入新数据
            return self.insert_df(table_name, df)
            
        except Exception as e:
            logger.error(f"Error upserting data into {table_name}: {e}")
            return False
    
    def query(self, sql: str, params: tuple = None) -> pd.DataFrame:
        """执行SQL查询"""
        try:
            if params:
                result = self.conn.execute(sql, params)
            else:
                result = self.conn.execute(sql)
            
            df = result.df()
            logger.debug(f"Query executed, returned {len(df)} rows")
            return df
            
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return pd.DataFrame()
    
    def read_table(self, table_name: str, where: str = None, 
                   columns: List[str] = None, limit: int = None) -> pd.DataFrame:
        """
        读取表数据
        
        Parameters:
        -----------
        table_name : str
            表名
        where : str, optional
            WHERE条件
        columns : List[str], optional
            要查询的列，默认为所有列
        limit : int, optional
            限制返回行数
            
        Returns:
        --------
        pd.DataFrame
            查询结果
        """
        try:
            if not self.table_exists(table_name):
                logger.warning(f"Table {table_name} does not exist")
                return pd.DataFrame()
            
            # 构建SQL
            cols = ", ".join(columns) if columns else "*"
            sql = f"SELECT {cols} FROM {table_name}"
            
            if where:
                sql += f" WHERE {where}"
            
            if limit:
                sql += f" LIMIT {limit}"
            
            return self.query(sql)
            
        except Exception as e:
            logger.error(f"Error reading table {table_name}: {e}")
            return pd.DataFrame()
    
    # ==================== 事务管理 ====================
    
    def begin_transaction(self):
        """开始事务"""
        try:
            self.conn.execute("BEGIN TRANSACTION")
            self._transaction_active = True
            logger.debug("Transaction started")
        except Exception as e:
            logger.error(f"Error starting transaction: {e}")
            raise
    
    def commit_transaction(self):
        """提交事务"""
        try:
            if self._transaction_active:
                self.conn.execute("COMMIT")
                self._transaction_active = False
                logger.debug("Transaction committed")
        except Exception as e:
            logger.error(f"Error committing transaction: {e}")
            raise
    
    def rollback_transaction(self):
        """回滚事务"""
        try:
            if self._transaction_active:
                self.conn.execute("ROLLBACK")
                self._transaction_active = False
                logger.debug("Transaction rolled back")
        except Exception as e:
            logger.error(f"Error rolling back transaction: {e}")
            raise
    
    # ==================== 工具方法 ====================
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            if not self.table_exists(table_name):
                return {}
            
            # 获取行数
            row_count = self.query(f"SELECT COUNT(*) as count FROM {table_name}")['count'].iloc[0]
            
            # 获取列信息
            columns = self.get_table_columns(table_name)
            
            return {
                'table_name': table_name,
                'row_count': row_count,
                'column_count': len(columns),
                'columns': columns
            }
            
        except Exception as e:
            logger.error(f"Error getting table info for {table_name}: {e}")
            return {}
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            # 获取所有表
            tables_df = self.query("SELECT name FROM sqlite_master WHERE type='table'")
            tables = tables_df['name'].tolist() if not tables_df.empty else []
            
            # 统计信息
            total_tables = len(tables)
            total_rows = 0
            
            for table in tables:
                try:
                    count_df = self.query(f"SELECT COUNT(*) as count FROM {table}")
                    total_rows += count_df['count'].iloc[0]
                except:
                    continue
            
            return {
                'database_path': self.db_path,
                'total_tables': total_tables,
                'total_rows': total_rows,
                'tables': tables,
                'file_size_mb': os.path.getsize(self.db_path) / (1024 * 1024) if os.path.exists(self.db_path) else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}
