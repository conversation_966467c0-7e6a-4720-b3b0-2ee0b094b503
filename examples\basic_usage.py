"""
因子研究框架基础使用示例

演示如何使用factor_research框架进行单因子分析。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 导入因子研究框架
from factor_research import FactorResearch
from factor_research.factors import MomentumFactor, RSIFactor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_data():
    """
    创建示例数据（模拟真实数据结构）
    
    Returns:
    --------
    pd.DataFrame
        示例价格数据
    """
    # 生成日期范围
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    dates = pd.date_range(start_date, end_date, freq='D')
    
    # 股票代码
    assets = ['000001', '000002', '000858', '002415', '600036']
    
    # 生成随机价格数据
    np.random.seed(42)
    data_list = []
    
    for asset in assets:
        # 生成价格序列（随机游走）
        returns = np.random.normal(0.001, 0.02, len(dates))
        prices = 100 * np.exp(np.cumsum(returns))
        
        # 生成成交量
        volumes = np.random.lognormal(15, 0.5, len(dates))
        
        for i, date in enumerate(dates):
            data_list.append({
                'date': date,
                'asset': asset,
                'close': prices[i],
                'volume': volumes[i],
                'open': prices[i] * (1 + np.random.normal(0, 0.005)),
                'high': prices[i] * (1 + abs(np.random.normal(0, 0.01))),
                'low': prices[i] * (1 - abs(np.random.normal(0, 0.01)))
            })
    
    df = pd.DataFrame(data_list)
    df = df.set_index(['date', 'asset'])
    
    logger.info(f"Created sample data: {len(df)} records for {len(assets)} assets")
    return df


def example_basic_factor_calculation():
    """
    示例1: 基础因子计算
    """
    logger.info("=== 示例1: 基础因子计算 ===")
    
    # 创建示例数据
    sample_data = create_sample_data()
    
    # 注意：简化架构不再使用FactorResearch类
    # 直接使用FactorEngine和FactorAnalyzer
    logger.info("使用简化架构：FactorEngine + FactorAnalyzer")

    # 这里演示手动计算因子（不依赖数据库）
    logger.info("手动计算因子示例：")
    
    # 手动计算动量因子
    momentum_factor = MomentumFactor(lookback_period=20, skip_period=1)
    momentum_data = momentum_factor.calculate(sample_data)
    
    logger.info(f"Momentum factor calculated: {len(momentum_data)} observations")
    logger.info(f"Momentum factor stats:\n{momentum_data.describe()}")
    
    return momentum_data


def example_factor_analysis():
    """
    示例2: 因子分析（模拟alphalens分析）
    """
    logger.info("=== 示例2: 因子分析 ===")
    
    # 获取因子数据
    momentum_data = example_basic_factor_calculation()
    
    # 创建模拟的价格数据用于分析
    sample_data = create_sample_data()
    
    # 注意：简化架构示例，不使用FactorResearch
    logger.info("简化架构演示：直接使用因子类进行计算")
    
    # 模拟因子分析（由于没有真实的alphalens，这里只做基础统计）
    logger.info("Performing basic factor analysis...")
    
    # 基础统计分析
    factor_stats = {
        'count': len(momentum_data),
        'mean': momentum_data.mean(),
        'std': momentum_data.std(),
        'min': momentum_data.min(),
        'max': momentum_data.max(),
        'skewness': momentum_data.skew(),
        'kurtosis': momentum_data.kurtosis()
    }
    
    logger.info("Factor Statistics:")
    for key, value in factor_stats.items():
        logger.info(f"  {key}: {value:.4f}")
    
    # 分位数分析
    quantiles = momentum_data.quantile([0.2, 0.4, 0.6, 0.8])
    logger.info(f"Factor Quantiles:\n{quantiles}")
    
    return factor_stats


def example_multiple_factors():
    """
    示例3: 多因子计算和比较
    """
    logger.info("=== 示例3: 多因子计算和比较 ===")
    
    # 创建示例数据
    sample_data = create_sample_data()
    
    # 计算多个因子
    factors = {}
    
    # 动量因子（不同参数）
    momentum_20d = MomentumFactor(lookback_period=20, skip_period=1)
    momentum_60d = MomentumFactor(lookback_period=60, skip_period=1)
    
    # RSI因子
    rsi_14d = RSIFactor(period=14)
    
    # 计算因子值
    factors['momentum_20d'] = momentum_20d.calculate(sample_data)
    factors['momentum_60d'] = momentum_60d.calculate(sample_data)
    factors['rsi_14d'] = rsi_14d.calculate(sample_data)
    
    # 比较因子统计
    logger.info("Factor Comparison:")
    comparison_stats = pd.DataFrame()
    
    for name, factor_data in factors.items():
        stats = pd.Series({
            'count': len(factor_data),
            'mean': factor_data.mean(),
            'std': factor_data.std(),
            'sharpe': factor_data.mean() / factor_data.std() if factor_data.std() > 0 else 0
        })
        comparison_stats[name] = stats
    
    logger.info(f"\n{comparison_stats}")
    
    # 因子相关性分析
    factor_df = pd.DataFrame(factors)
    correlation_matrix = factor_df.corr()
    
    logger.info(f"Factor Correlation Matrix:\n{correlation_matrix}")
    
    return factors, comparison_stats


def example_factor_info():
    """
    示例4: 获取因子信息
    """
    logger.info("=== 示例4: 因子信息查询 ===")
    
    # 创建因子实例
    momentum_factor = MomentumFactor(lookback_period=20)
    rsi_factor = RSIFactor(period=14)
    
    # 获取因子信息
    momentum_info = momentum_factor.get_factor_info()
    rsi_info = rsi_factor.get_factor_info()
    
    logger.info("Momentum Factor Info:")
    for key, value in momentum_info.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("\nRSI Factor Info:")
    for key, value in rsi_info.items():
        logger.info(f"  {key}: {value}")


def example_config_usage():
    """
    示例5: 配置使用
    """
    logger.info("=== 示例5: 配置管理 ===")
    
    from factor_research.utils.config import FactorConfig
    
    # 创建配置
    config = FactorConfig()
    
    # 查看默认配置
    logger.info("Default Configuration:")
    logger.info(f"  Cache enabled: {config.get('data_source.cache_enabled')}")
    logger.info(f"  Default periods: {config.get('alphalens_settings.periods')}")
    logger.info(f"  Figure size: {config.get('visualization.figure_size')}")
    
    # 修改配置
    config.set('alphalens_settings.periods', [1, 5, 10, 20, 60])
    config.set('visualization.dpi', 150)
    
    logger.info("Modified Configuration:")
    logger.info(f"  New periods: {config.get('alphalens_settings.periods')}")
    logger.info(f"  New DPI: {config.get('visualization.dpi')}")
    
    # 保存配置
    config.save_to_file('examples/sample_config.yaml')
    logger.info("Configuration saved to examples/sample_config.yaml")


def main():
    """
    主函数，运行所有示例
    """
    logger.info("Starting Factor Research Framework Examples")
    
    try:
        # 运行各个示例
        example_basic_factor_calculation()
        print("\n" + "="*50 + "\n")
        
        example_factor_analysis()
        print("\n" + "="*50 + "\n")
        
        example_multiple_factors()
        print("\n" + "="*50 + "\n")
        
        example_factor_info()
        print("\n" + "="*50 + "\n")
        
        example_config_usage()
        
        logger.info("All examples completed successfully!")
        
    except Exception as e:
        logger.error(f"Error running examples: {str(e)}")
        raise


if __name__ == "__main__":
    main()
