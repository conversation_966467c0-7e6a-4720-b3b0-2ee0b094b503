"""
Common模块统一入口

提供统一的配置、日志、异常处理和性能优化功能。
简化模块导入，提供一站式的通用功能访问。
"""

# 统一配置管理
from .unified_config import (
    UnifiedConfig, 
    get_config, 
    reload_config,
    DatabaseConfig,
    DataEngineConfig,
    FactorResearchConfig,
    GUIConfig,
    LoggingConfig,
    TradingConfig
)

# 统一日志管理
from .unified_logger import (
    UnifiedLogger,
    get_logger,
    setup_logging,
    debug, info, warning, error, critical, exception
)

# 统一异常处理
from .unified_exceptions import (
    TTradeException,
    DataException, DataNotFoundError, DataFormatError, DataValidationError,
    DatabaseConnectionError, DatabaseOperationError,
    FactorException, FactorCalculationError, FactorAnalysisError, 
    FactorDataError, AlphalensError,
    TradingException, OrderError, PositionError, RiskControlError, AccountError,
    GUIException, PanelError, VisualizationError,
    ConfigException, ConfigLoadError, ConfigValidationError,
    NetworkException, APIError, TimeoutError, RateLimitError,
    handle_exceptions, safe_execute, ErrorContext,
    get_exception_manager, record_exception,
    create_error, raise_error, validate_data
)

# 性能管理工具
from .performance_optimizer import (
    PerformanceMonitor, FunctionProfiler, CacheManager, MemoryManager,
    PerformanceManager, get_performance_manager, profile, manage_performance
)

# 代码质量检查
from .code_quality import (
    CodeComplexityAnalyzer, SOLIDPrincipleChecker, CodeQualityChecker,
    analyze_code_quality
)

# 向后兼容的导入
from .config import DATA_BASE_DIR
from .data_utils import *

__all__ = [
    # 配置管理
    'UnifiedConfig', 'get_config', 'reload_config',
    'DatabaseConfig', 'DataEngineConfig', 'FactorResearchConfig',
    'GUIConfig', 'LoggingConfig', 'TradingConfig',
    
    # 日志管理
    'UnifiedLogger', 'get_logger', 'setup_logging',
    'debug', 'info', 'warning', 'error', 'critical', 'exception',
    
    # 异常处理
    'TTradeException',
    'DataException', 'DataNotFoundError', 'DataFormatError', 'DataValidationError',
    'DatabaseConnectionError', 'DatabaseOperationError',
    'FactorException', 'FactorCalculationError', 'FactorAnalysisError',
    'FactorDataError', 'AlphalensError',
    'TradingException', 'OrderError', 'PositionError', 'RiskControlError', 'AccountError',
    'GUIException', 'PanelError', 'VisualizationError',
    'ConfigException', 'ConfigLoadError', 'ConfigValidationError',
    'NetworkException', 'APIError', 'TimeoutError', 'RateLimitError',
    'handle_exceptions', 'safe_execute', 'ErrorContext',
    'get_exception_manager', 'record_exception',
    'create_error', 'raise_error', 'validate_data',
    
    # 性能管理
    'PerformanceMonitor', 'FunctionProfiler', 'CacheManager', 'MemoryManager',
    'PerformanceManager', 'get_performance_manager', 'profile', 'manage_performance',
    
    # 代码质量
    'CodeComplexityAnalyzer', 'SOLIDPrincipleChecker', 'CodeQualityChecker',
    'analyze_code_quality',
    
    # 向后兼容
    'DATA_BASE_DIR'
]

__version__ = "2.1.0"


def initialize_common_systems():
    """
    初始化通用系统
    
    设置日志、配置、异常处理等系统
    """
    try:
        # 初始化配置系统
        config = get_config()
        
        # 初始化日志系统
        logger = setup_logging()
        logger.info("Common systems initialized successfully")
        
        # 初始化性能监控
        performance_manager = get_performance_manager()
        performance_manager.start_monitoring()
        
        # 初始化异常管理
        exception_manager = get_exception_manager()
        
        return {
            'config': config,
            'logger': logger,
            'performance_manager': performance_manager,
            'exception_manager': exception_manager
        }
        
    except Exception as e:
        print(f"Error initializing common systems: {e}")
        return None


def cleanup_common_systems():
    """
    清理通用系统
    
    停止监控、关闭日志等
    """
    try:
        # 停止性能监控
        performance_manager = get_performance_manager()
        performance_manager.stop_monitoring()
        
        # 关闭日志系统
        logger = get_logger()
        logger.info("Common systems cleanup completed")
        logger.close()
        
    except Exception as e:
        print(f"Error cleaning up common systems: {e}")


# 自动初始化（可选）
_auto_init = False

if _auto_init:
    initialize_common_systems()
