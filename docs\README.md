# T_TRADE 项目文档中心

> **版本**: v2.0 | **更新**: 2025-06-29

## 📚 文档导航

### 🎯 AI开发助手专用
- **[Augment Guideline](augment-guideline.md)** - 完整开发指导文档 (主要参考)
- **[Quick Reference](quick-reference.md)** - 快速参考指南 (日常查阅)
- **[Rules](rules.md)** - AI开发规则与技术规范 (强制要求)

### 📖 用户文档
- **[项目README](../README.md)** - 项目概览和安装指南
- **[因子研究指南](../factor_research/README.md)** - 因子研究框架文档
- **[GUI使用指南](../gui/README_FACTOR_RESEARCH.md)** - 图形界面使用说明

### 🏗️ 技术文档
- **[架构设计](trade_engine/)** - 系统架构和设计文档
- **[API文档](../factor_research/doc/)** - 接口和使用文档

## 🚀 快速开始

### 对于AI开发助手
1. **必读**: [Augment Guideline](augment-guideline.md) - 了解完整开发规范
2. **常用**: [Quick Reference](quick-reference.md) - 日常开发快速查阅
3. **强制**: [Rules](rules.md) - 必须遵循的技术规范

### 对于开发者
1. **安装**: 参考 [项目README](../README.md) 进行环境配置
2. **开发**: 遵循 [Augment Guideline](augment-guideline.md) 中的开发规范
3. **测试**: 使用 `python test/run_tests.py` 运行测试

### 对于用户
1. **安装**: 按照安装指南配置环境
2. **使用**: 参考GUI使用指南操作界面
3. **问题**: 查看故障排除部分或查看日志文件

## 🎯 核心原则

### 1. 第一性原理
- 数据驱动决策
- 模块化设计
- 配置外置管理
- 优雅错误处理

### 2. 代码质量
- 遵循SOLID原则
- DRY & KISS原则
- 完整测试覆盖
- 清晰文档注释

### 3. 技术规范
- **强制**: 中文字体正确设置
- **强制**: DataFrame布尔评估修复
- **推荐**: 类型提示和文档字符串
- **推荐**: 错误处理和日志记录

## 🏗️ 项目架构

```
T_TRADE 量化交易系统
├── 用户界面层 (GUI Layer)
│   ├── MainFrame (主窗口)
│   ├── PanelFactorResearch (因子研究面板)
│   └── FactorChartWidget (图表组件)
├── 业务逻辑层 (Business Layer)  
│   ├── FactorEngine (因子计算引擎)
│   ├── FactorAnalyzer (因子分析器)
│   └── TradeEngine (交易引擎)
├── 数据访问层 (Data Layer)
│   ├── DataAdapter (数据适配器)
│   ├── DBManage (数据库管理)
│   └── DataEngine (数据引擎)
└── 存储层 (Storage Layer)
    ├── DuckDB (主数据库)
    ├── Cache (缓存系统)
    └── Files (文件存储)
```

## 🔧 开发工具

### 环境检查
```bash
python scripts/check_environment.py
```

### 测试运行
```bash
python test/run_tests.py
```

### 项目清理
```bash
python scripts/cleanup_project.py --execute
```

### 代码格式化
```bash
black --line-length 88 .
isort .
```

## 📊 质量指标

### 测试覆盖率
- 核心业务逻辑: 90%+
- 数据处理模块: 80%+
- GUI模块: 基本功能测试

### 代码质量
- PEP 8 编码规范
- 类型提示覆盖
- 文档字符串完整
- 错误处理完善

## 🚨 重要提醒

### AI开发助手必须遵循
1. **中文字体**: 所有matplotlib图表必须正确设置中文字体
2. **DataFrame布尔**: 修复所有DataFrame布尔评估错误
3. **错误处理**: 实现分层错误处理和用户友好提示
4. **测试覆盖**: 核心功能必须有对应测试
5. **文档同步**: 代码变更时同步更新文档

### 常见错误避免
- ❌ `if dataframe:` → ✅ `if not dataframe.empty:`
- ❌ 不设置中文字体 → ✅ 强制设置字体
- ❌ 硬编码配置 → ✅ 使用配置管理器
- ❌ 忽略错误处理 → ✅ 完善异常处理

## 📞 支持与反馈

### 日志文件
- `logs/development.log` - 开发环境日志
- `logs/development_error.log` - 错误日志
- `logs/production.log` - 生产环境日志

### 故障排除
1. 检查日志文件中的错误信息
2. 运行环境检查脚本
3. 查看相关文档说明
4. 检查配置文件设置

### 贡献指南
1. Fork项目仓库
2. 创建功能分支
3. 遵循开发规范
4. 提交Pull Request

---

**T_TRADE 文档中心 v2.0**  
**维护**: AI开发团队  
**更新**: 2025-06-29
