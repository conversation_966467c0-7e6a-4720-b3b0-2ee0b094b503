"""
REST API服务

提供HTTP接口访问量化交易系统的核心功能。
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Web框架
try:
    from flask import Flask, request, jsonify, Response
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    Flask = None
    CORS = None

# 项目模块
from database.db_manage import DBManage
from portfolio.account.account_manage import AccountManage
from factor_research import FactorResearch
from common.config_manager import get_config
from common.cache_manager import cache_manager
from common.performance_monitor import performance_monitor
from logger.enhanced_logger import get_logger

logger = get_logger(__name__)


class QuantAPI:
    """量化交易API服务"""
    
    def __init__(self):
        """初始化API服务"""
        if not FLASK_AVAILABLE:
            raise ImportError("Flask is required for API service. Install with: pip install flask flask-cors")
        
        self.app = Flask(__name__)
        CORS(self.app)  # 启用跨域支持
        
        # 初始化核心组件
        self._init_components()
        
        # 注册路由
        self._register_routes()
        
        logger.info("QuantAPI initialized")
    
    def _init_components(self):
        """初始化核心组件"""
        try:
            # 初始化数据库管理器
            self.account_manage = AccountManage()
            self.db_manage = DBManage(self.account_manage)
            
            # 初始化因子研究框架
            self.factor_research = FactorResearch(self.db_manage)
            
            logger.info("Core components initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    def _register_routes(self):
        """注册API路由"""
        
        # 健康检查
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查接口"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        # 系统信息
        @self.app.route('/api/v1/system/info', methods=['GET'])
        def get_system_info():
            """获取系统信息"""
            try:
                # 获取性能报告
                perf_report = performance_monitor.get_performance_report()
                
                # 获取缓存统计
                cache_stats = cache_manager.get_stats()
                
                # 获取配置信息
                config_info = {
                    'database_path': get_config('database', 'path'),
                    'environment': os.getenv('T_TRADE_ENV', 'development')
                }
                
                return jsonify({
                    'status': 'success',
                    'data': {
                        'performance': perf_report,
                        'cache': cache_stats,
                        'config': config_info,
                        'timestamp': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                logger.error(f"System info error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        # 数据接口
        @self.app.route('/api/v1/data/stocks', methods=['GET'])
        def get_stock_list():
            """获取股票列表"""
            try:
                # 从数据库获取股票列表
                query = "SELECT DISTINCT code FROM stock_daily ORDER BY code"
                result = self.db_manage.quant_db.conn.execute(query).fetchall()
                
                stocks = [{'code': row[0]} for row in result]
                
                return jsonify({
                    'status': 'success',
                    'data': {
                        'stocks': stocks,
                        'count': len(stocks)
                    }
                })
                
            except Exception as e:
                logger.error(f"Get stock list error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        @self.app.route('/api/v1/data/stocks/<stock_code>/price', methods=['GET'])
        def get_stock_price(stock_code):
            """获取股票价格数据"""
            try:
                # 获取查询参数
                start_date = request.args.get('start_date')
                end_date = request.args.get('end_date')
                limit = request.args.get('limit', 100, type=int)
                
                # 构建查询
                query = f"""
                SELECT date, code, open, high, low, close, volume, amount
                FROM stock_daily 
                WHERE code = '{stock_code}'
                """
                
                if start_date:
                    query += f" AND date >= '{start_date}'"
                if end_date:
                    query += f" AND date <= '{end_date}'"
                
                query += f" ORDER BY date DESC LIMIT {limit}"
                
                # 执行查询
                result = self.db_manage.quant_db.conn.execute(query).fetchall()
                
                # 格式化结果
                price_data = []
                for row in result:
                    price_data.append({
                        'date': row[0],
                        'code': row[1],
                        'open': row[2],
                        'high': row[3],
                        'low': row[4],
                        'close': row[5],
                        'volume': row[6],
                        'amount': row[7]
                    })
                
                return jsonify({
                    'status': 'success',
                    'data': {
                        'stock_code': stock_code,
                        'price_data': price_data,
                        'count': len(price_data)
                    }
                })
                
            except Exception as e:
                logger.error(f"Get stock price error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        # 因子研究接口
        @self.app.route('/api/v1/factor/calculate', methods=['POST'])
        def calculate_factor():
            """计算因子"""
            try:
                data = request.get_json()
                
                # 验证必要参数
                required_params = ['factor_name', 'assets']
                for param in required_params:
                    if param not in data:
                        return jsonify({
                            'status': 'error',
                            'message': f'Missing required parameter: {param}'
                        }), 400
                
                # 获取参数
                factor_name = data['factor_name']
                assets = data['assets']
                start_date = data.get('start_date')
                end_date = data.get('end_date')
                lookback_period = data.get('lookback_period', 20)
                
                # 计算因子
                factor_data = self.factor_research.calculate_factor(
                    factor_name=factor_name,
                    assets=assets,
                    start_date=start_date,
                    end_date=end_date,
                    lookback_period=lookback_period
                )
                
                if factor_data is not None and not factor_data.empty:
                    # 转换为JSON格式
                    factor_json = factor_data.reset_index().to_dict('records')
                    
                    return jsonify({
                        'status': 'success',
                        'data': {
                            'factor_name': factor_name,
                            'factor_data': factor_json,
                            'count': len(factor_json)
                        }
                    })
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'No factor data calculated'
                    }), 404
                
            except Exception as e:
                logger.error(f"Calculate factor error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        @self.app.route('/api/v1/factor/list', methods=['GET'])
        def get_factor_list():
            """获取可用因子列表"""
            try:
                # 获取因子引擎中的可用因子
                available_factors = []
                
                if hasattr(self.factor_research, 'factor_engine'):
                    factor_engine = self.factor_research.factor_engine
                    if hasattr(factor_engine, 'get_available_factors'):
                        available_factors = factor_engine.get_available_factors()
                    else:
                        # 默认因子列表
                        available_factors = [
                            'MomentumFactor',
                            'ReversalFactor',
                            'VolatilityFactor',
                            'VolumeRatioFactor'
                        ]
                
                return jsonify({
                    'status': 'success',
                    'data': {
                        'factors': available_factors,
                        'count': len(available_factors)
                    }
                })
                
            except Exception as e:
                logger.error(f"Get factor list error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        # 数据库优化接口
        @self.app.route('/api/v1/database/optimize', methods=['POST'])
        def optimize_database():
            """优化数据库"""
            try:
                # 运行高级数据库优化
                results = self.db_manage.run_advanced_optimization()
                
                if results:
                    return jsonify({
                        'status': 'success',
                        'data': {
                            'optimization_results': results,
                            'message': 'Database optimization completed'
                        }
                    })
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'Database optimization failed'
                    }), 500
                
            except Exception as e:
                logger.error(f"Database optimization error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        @self.app.route('/api/v1/database/slow-queries', methods=['GET'])
        def get_slow_queries():
            """获取慢查询报告"""
            try:
                report = self.db_manage.get_slow_queries_report()
                
                return jsonify({
                    'status': 'success',
                    'data': report or {'message': 'No slow queries detected'}
                })
                
            except Exception as e:
                logger.error(f"Get slow queries error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        # 缓存管理接口
        @self.app.route('/api/v1/cache/stats', methods=['GET'])
        def get_cache_stats():
            """获取缓存统计"""
            try:
                stats = cache_manager.get_stats()
                
                return jsonify({
                    'status': 'success',
                    'data': stats
                })
                
            except Exception as e:
                logger.error(f"Get cache stats error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        @self.app.route('/api/v1/cache/clear', methods=['POST'])
        def clear_cache():
            """清理缓存"""
            try:
                cache_type = request.get_json().get('type', 'auto') if request.is_json else 'auto'
                cache_manager.clear(cache_type)
                
                return jsonify({
                    'status': 'success',
                    'message': f'Cache cleared: {cache_type}'
                })
                
            except Exception as e:
                logger.error(f"Clear cache error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """运行API服务"""
        logger.info(f"Starting API server on {host}:{port}")
        
        try:
            self.app.run(host=host, port=port, debug=debug)
        except Exception as e:
            logger.error(f"Failed to start API server: {e}")
            raise


def create_app():
    """创建Flask应用"""
    api = QuantAPI()
    return api.app


def main():
    """主函数"""
    print("🚀 启动量化交易REST API服务")
    print("=" * 50)
    
    try:
        # 创建API服务
        api = QuantAPI()
        
        # 获取配置
        host = get_config('api', 'host') or '127.0.0.1'
        port = get_config('api', 'port') or 5000
        debug = get_config('api', 'debug') or False
        
        print(f"📡 API服务地址: http://{host}:{port}")
        print(f"📖 API文档: http://{host}:{port}/health")
        print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
        
        print("\n💡 可用接口:")
        print("  GET  /health                     - 健康检查")
        print("  GET  /api/v1/system/info         - 系统信息")
        print("  GET  /api/v1/data/stocks         - 股票列表")
        print("  GET  /api/v1/data/stocks/{code}/price - 股票价格")
        print("  POST /api/v1/factor/calculate    - 计算因子")
        print("  GET  /api/v1/factor/list         - 因子列表")
        print("  POST /api/v1/database/optimize   - 数据库优化")
        print("  GET  /api/v1/database/slow-queries - 慢查询报告")
        print("  GET  /api/v1/cache/stats         - 缓存统计")
        print("  POST /api/v1/cache/clear         - 清理缓存")
        
        print(f"\n按 Ctrl+C 停止服务...")
        
        # 启动服务
        api.run(host=host, port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号")
        print("✅ API服务已停止")
    
    except Exception as e:
        print(f"❌ API服务启动失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
