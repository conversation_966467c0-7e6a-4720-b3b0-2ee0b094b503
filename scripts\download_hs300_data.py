#!/usr/bin/env python3
"""
沪深300数据下载脚本

手动下载沪深300核心股票数据到数据库中。
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def download_hs300_data():
    """下载沪深300数据"""
    print("🚀 开始下载沪深300核心数据")
    print("=" * 50)
    
    try:
        from database.db_manage import DBManage
        from portfolio.account.account_manage import AccountManage
        
        # 初始化数据库管理器
        print("📊 初始化数据库管理器...")
        account_manage = AccountManage()
        db_manage = DBManage(account_manage)
        
        print("✓ 数据库管理器初始化成功")
        
        # 初始化数据库
        print("📊 初始化数据库...")
        db_manage.init_database()
        print("✓ 数据库初始化完成")
        
        # 获取沪深300股票列表
        print("📊 获取沪深300股票列表...")
        hs300_stocks = db_manage._get_hs300_stock_list()
        print(f"✓ 获取到{len(hs300_stocks)}只沪深300股票")
        
        # 显示股票列表概览
        print(f"📋 股票列表概览:")
        print(f"  前10只: {hs300_stocks[:10]}")
        print(f"  后10只: {hs300_stocks[-10:]}")
        
        # 计算下载日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3*365)  # 最近3年
        
        print(f"📅 下载日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 询问用户确认
        response = input("\n是否开始下载？这可能需要一些时间。(y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ 用户取消下载")
            return False
        
        # 开始下载
        print("\n🔄 开始下载数据...")
        
        # 分批下载
        batch_size = 5  # 每批5只股票，避免请求过于频繁
        total_stocks = len(hs300_stocks)
        success_count = 0
        error_count = 0
        
        for i in range(0, total_stocks, batch_size):
            batch_stocks = hs300_stocks[i:i+batch_size]
            batch_num = i // batch_size + 1
            total_batches = (total_stocks + batch_size - 1) // batch_size
            
            print(f"\n📦 处理第{batch_num}/{total_batches}批股票: {batch_stocks}")
            
            for stock_code in batch_stocks:
                try:
                    print(f"  📈 下载 {stock_code}...")
                    
                    # 下载历史数据
                    db_manage._download_stock_history(stock_code, start_date, end_date)
                    
                    # 下载实时数据
                    db_manage._download_stock_realtime(stock_code)
                    
                    success_count += 1
                    print(f"  ✓ {stock_code} 下载成功")
                    
                except Exception as e:
                    error_count += 1
                    print(f"  ❌ {stock_code} 下载失败: {e}")
            
            # 批次间暂停
            if i + batch_size < total_stocks:
                print(f"  ⏸️ 暂停2秒...")
                import time
                time.sleep(2)
        
        # 下载完成统计
        print(f"\n📊 下载完成统计:")
        print(f"  总股票数: {total_stocks}")
        print(f"  成功下载: {success_count}")
        print(f"  下载失败: {error_count}")
        print(f"  成功率: {success_count/total_stocks*100:.1f}%")
        
        if success_count > 0:
            print(f"\n✅ 数据下载完成！")
            
            # 验证数据
            print(f"🔍 验证下载的数据...")
            verify_downloaded_data(db_manage, hs300_stocks[:5])  # 验证前5只股票
            
            return True
        else:
            print(f"\n❌ 所有股票下载都失败了")
            return False
        
    except Exception as e:
        print(f"❌ 下载过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_downloaded_data(db_manage, sample_stocks):
    """验证下载的数据"""
    try:
        # 检查实时数据表
        print(f"  检查实时数据表...")
        
        for stock_code in sample_stocks:
            try:
                query = f"SELECT * FROM stock_realtime WHERE code = '{stock_code}' LIMIT 1"
                result = db_manage.quant_db.conn.execute(query).fetchone()
                
                if result:
                    print(f"    ✓ {stock_code}: 实时数据存在")
                else:
                    print(f"    ⚠️ {stock_code}: 实时数据不存在")
                    
            except Exception as e:
                print(f"    ❌ {stock_code}: 检查失败 - {e}")
        
        # 统计总数据量
        try:
            total_count = db_manage.quant_db.conn.execute("SELECT COUNT(*) FROM stock_realtime").fetchone()[0]
            print(f"  📊 实时数据表总记录数: {total_count}")
        except Exception as e:
            print(f"  ❌ 统计总数据量失败: {e}")
        
        print(f"✓ 数据验证完成")
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")

def test_factor_research_with_downloaded_data():
    """测试使用下载的数据进行因子研究"""
    print(f"\n🧪 测试因子研究功能...")
    
    try:
        from factor_research.core.data_adapter import DataAdapter
        from database.db_manage import DBManage
        from portfolio.account.account_manage import AccountManage
        
        # 初始化组件
        account_manage = AccountManage()
        db_manage = DBManage(account_manage)
        data_adapter = DataAdapter(db_manage=db_manage, cache_enabled=False)
        
        # 获取一些股票进行测试
        hs300_stocks = db_manage._get_hs300_stock_list()
        test_stocks = hs300_stocks[:3]
        
        print(f"  测试股票: {test_stocks}")
        
        # 测试数据获取
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        data = data_adapter.get_price_data(
            assets=test_stocks,
            start_date=current_date,
            end_date=current_date,
            fields=['close', 'volume']
        )
        
        if not data.empty:
            print(f"  ✅ 数据获取成功: {data.shape}")
            print(f"  ✅ 包含字段: {data.columns.tolist()}")
            print(f"  ✅ 因子研究可以使用真实数据！")
            return True
        else:
            print(f"  ❌ 数据获取失败")
            return False
        
    except Exception as e:
        print(f"  ❌ 因子研究测试失败: {e}")
        return False

def main():
    """主函数"""
    print("📈 沪深300数据下载工具")
    print("=" * 50)
    print("此工具将下载沪深300核心股票的历史数据和实时数据")
    print("下载的数据将用于因子研究和量化分析")
    print("=" * 50)
    
    # 下载数据
    if download_hs300_data():
        # 测试因子研究功能
        test_factor_research_with_downloaded_data()
        
        print(f"\n🎉 沪深300数据下载完成！")
        print(f"\n💡 下一步:")
        print(f"  1. 运行 python main.py 启动主程序")
        print(f"  2. 打开因子研究面板")
        print(f"  3. 选择沪深300股票进行因子分析")
        print(f"  4. 享受基于真实数据的量化研究！")
        
        return 0
    else:
        print(f"\n❌ 数据下载失败")
        print(f"\n🔧 故障排除:")
        print(f"  1. 检查网络连接")
        print(f"  2. 检查数据源API是否可用")
        print(f"  3. 检查数据库权限")
        print(f"  4. 查看详细错误日志")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
