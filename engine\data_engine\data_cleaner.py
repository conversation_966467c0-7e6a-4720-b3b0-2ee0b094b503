"""
数据清洗模块

在data_engine下载数据后立即进行清洗处理，确保存储到数据库的数据：
1. 时区统一为UTC
2. 无NaN值或已妥善处理
3. 数据类型正确
4. 索引格式标准化
5. 资产名称标准化

这样factor_research模块可以直接使用清洗后的数据，无需重复处理。
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class DataCleaner:
    """数据清洗器，在数据下载后立即进行清洗处理"""
    
    def __init__(self):
        """初始化数据清洗器"""
        self.cleaning_stats = {
            'processed_records': 0,
            'cleaned_records': 0,
            'removed_records': 0,
            'fixed_timezones': 0,
            'normalized_assets': 0
        }
    
    def clean_stock_data(self, df: pd.DataFrame, data_source: str = "unknown") -> pd.DataFrame:
        """
        清洗股票数据
        
        Parameters:
        -----------
        df : pd.DataFrame
            原始股票数据
        data_source : str
            数据源标识
            
        Returns:
        --------
        pd.DataFrame
            清洗后的数据
        """
        try:
            logger.info(f"Starting data cleaning for {len(df)} records from {data_source}")
            self.cleaning_stats['processed_records'] += len(df)
            
            if df.empty:
                logger.warning("Empty DataFrame provided for cleaning")
                return df
            
            # 1. 标准化列名
            df_clean = self._standardize_columns(df)
            
            # 2. 处理日期和时区
            df_clean = self._process_datetime_and_timezone(df_clean)
            
            # 3. 清理数值数据
            df_clean = self._clean_numeric_data(df_clean)
            
            # 4. 标准化资产名称
            df_clean = self._standardize_asset_names(df_clean)
            
            # 5. 处理缺失值
            df_clean = self._handle_missing_values(df_clean)
            
            # 6. 验证数据质量
            df_clean = self._validate_data_quality(df_clean)
            
            # 7. 设置标准索引
            df_clean = self._set_standard_index(df_clean)
            
            self.cleaning_stats['cleaned_records'] += len(df_clean)
            self.cleaning_stats['removed_records'] += len(df) - len(df_clean)
            
            logger.info(f"Data cleaning completed: {len(df_clean)} records cleaned, {len(df) - len(df_clean)} records removed")
            return df_clean
            
        except Exception as e:
            logger.error(f"Error in data cleaning: {e}")
            raise
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        try:
            # 标准列名映射
            column_mapping = {
                # 中文列名
                '日期': 'date',
                '代码': 'code', 
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low', 
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'amount',
                
                # 英文列名标准化
                'Date': 'date',
                'Code': 'code',
                'Open': 'open', 
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume',
                'Amount': 'amount',
                
                # 其他可能的列名
                'symbol': 'code',
                'Symbol': 'code',
                'asset': 'code',
                'Asset': 'code'
            }
            
            # 应用列名映射
            df_renamed = df.rename(columns=column_mapping)
            
            # 确保必要列存在
            required_columns = ['date', 'code', 'close']
            missing_columns = [col for col in required_columns if col not in df_renamed.columns]
            
            if missing_columns:
                logger.warning(f"Missing required columns: {missing_columns}")
                # 尝试从现有列推断
                if 'code' not in df_renamed.columns and 'asset' in df_renamed.columns:
                    df_renamed['code'] = df_renamed['asset']
            
            logger.debug(f"Standardized columns: {list(df_renamed.columns)}")
            return df_renamed
            
        except Exception as e:
            logger.error(f"Error standardizing columns: {e}")
            return df
    
    def _process_datetime_and_timezone(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理日期时间和时区"""
        try:
            if 'date' not in df.columns:
                logger.warning("No date column found")
                return df
            
            # 转换为datetime
            if not pd.api.types.is_datetime64_any_dtype(df['date']):
                df['date'] = pd.to_datetime(df['date'], errors='coerce')
            
            # 统一时区处理
            if df['date'].dt.tz is None:
                # 无时区，添加UTC时区
                df['date'] = df['date'].dt.tz_localize('UTC')
                self.cleaning_stats['fixed_timezones'] += len(df)
                logger.debug("Added UTC timezone to date column")
            else:
                # 有时区，转换为UTC
                df['date'] = df['date'].dt.tz_convert('UTC')
                self.cleaning_stats['fixed_timezones'] += len(df)
                logger.debug(f"Converted timezone to UTC from {df['date'].dt.tz}")
            
            # 标准化为日期（去除时间部分）
            df['date'] = df['date'].dt.normalize()
            
            return df
            
        except Exception as e:
            logger.error(f"Error processing datetime and timezone: {e}")
            return df
    
    def _clean_numeric_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理数值数据"""
        try:
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            
            for col in numeric_columns:
                if col in df.columns:
                    # 转换为数值类型
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    # 处理无穷值
                    df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                    
                    # 处理负值（价格和成交量不应该为负）
                    if col in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                        df.loc[df[col] < 0, col] = np.nan
            
            logger.debug("Numeric data cleaning completed")
            return df
            
        except Exception as e:
            logger.error(f"Error cleaning numeric data: {e}")
            return df
    
    def _standardize_asset_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化资产名称"""
        try:
            if 'code' not in df.columns:
                logger.warning("No code column found for asset name standardization")
                return df
            
            def normalize_stock_code(code):
                """标准化股票代码"""
                code = str(code).strip().upper()
                
                # 处理6位数字代码
                if len(code) == 6 and code.isdigit():
                    if code.startswith(('000', '001', '002', '003', '300')):
                        return f"{code}.SZ"  # 深圳
                    elif code.startswith(('600', '601', '603', '605', '688')):
                        return f"{code}.SH"  # 上海
                    else:
                        return code
                
                # 已有市场后缀的保持原样
                if '.' in code:
                    return code
                
                return code
            
            # 应用标准化
            df['asset'] = df['code'].apply(normalize_stock_code)
            self.cleaning_stats['normalized_assets'] += len(df)
            
            logger.debug("Asset names standardized")
            return df
            
        except Exception as e:
            logger.error(f"Error standardizing asset names: {e}")
            return df
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        try:
            # 记录原始缺失值情况
            missing_before = df.isnull().sum().sum()
            
            # 对于价格数据，使用前向填充
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df.columns:
                    df[col] = df[col].ffill().bfill()
            
            # 对于成交量，用0填充
            volume_columns = ['volume', 'amount']
            for col in volume_columns:
                if col in df.columns:
                    df[col] = df[col].fillna(0)
            
            # 删除关键字段仍为空的记录
            critical_columns = ['date', 'code', 'close']
            df = df.dropna(subset=[col for col in critical_columns if col in df.columns])
            
            missing_after = df.isnull().sum().sum()
            logger.debug(f"Missing values: {missing_before} → {missing_after}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error handling missing values: {e}")
            return df
    
    def _validate_data_quality(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据质量"""
        try:
            initial_count = len(df)
            
            # 删除重复记录
            if 'date' in df.columns and 'asset' in df.columns:
                df = df.drop_duplicates(subset=['date', 'asset'])
            
            # 验证价格数据合理性
            if 'close' in df.columns:
                # 删除价格为0或异常的记录
                df = df[df['close'] > 0]
                
                # 删除价格变化异常的记录（日涨跌幅超过50%的可能是数据错误）
                if 'open' in df.columns:
                    price_change = abs(df['close'] - df['open']) / df['open']
                    df = df[price_change <= 0.5]  # 50%涨跌幅限制
            
            # 验证日期合理性
            if 'date' in df.columns:
                # 删除未来日期的记录
                today = pd.Timestamp.now(tz='UTC').normalize()
                df = df[df['date'] <= today]
                
                # 删除过于久远的记录（超过20年）
                twenty_years_ago = today - pd.Timedelta(days=20*365)
                df = df[df['date'] >= twenty_years_ago]
            
            removed_count = initial_count - len(df)
            if removed_count > 0:
                logger.debug(f"Removed {removed_count} records during quality validation")
            
            return df
            
        except Exception as e:
            logger.error(f"Error in data quality validation: {e}")
            return df
    
    def _set_standard_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """设置标准索引"""
        try:
            # 确保有必要的列
            if 'date' not in df.columns or 'asset' not in df.columns:
                logger.warning("Cannot set standard index: missing date or asset column")
                return df
            
            # 设置MultiIndex
            df = df.set_index(['date', 'asset'])
            
            # 确保索引名称正确
            df.index.names = ['date', 'asset']
            
            logger.debug("Standard index set successfully")
            return df
            
        except Exception as e:
            logger.error(f"Error setting standard index: {e}")
            return df
    
    def get_cleaning_stats(self) -> Dict[str, int]:
        """获取清洗统计信息"""
        return self.cleaning_stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.cleaning_stats = {
            'processed_records': 0,
            'cleaned_records': 0,
            'removed_records': 0,
            'fixed_timezones': 0,
            'normalized_assets': 0
        }

    def clean_factor_data_for_analysis(self, factor_data: pd.Series, data_source: str = "unknown") -> pd.Series:
        """
        专门为因子分析清洗因子数据

        Parameters:
        -----------
        factor_data : pd.Series
            原始因子数据，可以是单级索引或MultiIndex
        data_source : str
            数据源标识

        Returns:
        --------
        pd.Series
            清洗后的因子数据，已设置标准MultiIndex和时区
        """
        try:
            logger.info(f"Cleaning factor data for analysis from {data_source}")

            # 1. 基本格式验证
            if not isinstance(factor_data, pd.Series):
                raise ValueError("Factor data must be a pandas Series")

            # 2. 确保MultiIndex格式
            factor_data = self._ensure_factor_multiindex(factor_data)

            # 3. 确保索引名称标准化
            if factor_data.index.names != ['date', 'asset']:
                factor_data.index.names = ['date', 'asset']

            # 4. 处理日期层级的时区
            factor_data = self._ensure_factor_datetime_index_with_timezone(factor_data)

            # 5. 标准化资产名称
            factor_data = self._standardize_factor_asset_names(factor_data)

            # 6. 处理缺失值
            factor_data = self._handle_factor_missing_values(factor_data)

            # 7. 验证数据质量
            factor_data = self._validate_factor_data_quality(factor_data)

            logger.info(f"Factor data cleaning completed: {factor_data.shape}")
            return factor_data

        except Exception as e:
            logger.error(f"Error cleaning factor data for analysis: {e}")
            raise

    def clean_price_data_for_analysis(self, price_data: pd.DataFrame, data_source: str = "unknown") -> pd.DataFrame:
        """
        专门为因子分析清洗价格数据

        Parameters:
        -----------
        price_data : pd.DataFrame
            原始价格数据，应该有DatetimeIndex和资产列
        data_source : str
            数据源标识

        Returns:
        --------
        pd.DataFrame
            清洗后的价格数据，已设置标准索引和时区
        """
        try:
            logger.info(f"Cleaning price data for analysis from {data_source}")

            # 1. 基本格式验证
            if not isinstance(price_data, pd.DataFrame):
                raise ValueError("Price data must be a pandas DataFrame")

            # 2. 确保索引是DatetimeIndex或MultiIndex
            if isinstance(price_data.index, pd.MultiIndex):
                # 如果是MultiIndex，检查第一级是否是datetime
                if not isinstance(price_data.index.get_level_values(0), pd.DatetimeIndex):
                    # 转换第一级为datetime
                    new_levels = [pd.to_datetime(price_data.index.get_level_values(0))]
                    new_levels.extend([price_data.index.get_level_values(i) for i in range(1, price_data.index.nlevels)])
                    price_data.index = pd.MultiIndex.from_arrays(new_levels, names=price_data.index.names)
                logger.debug(f"Price data has MultiIndex with {price_data.index.nlevels} levels")
            elif not isinstance(price_data.index, pd.DatetimeIndex):
                if 'date' in price_data.columns:
                    price_data = price_data.set_index('date')
                else:
                    price_data.index = pd.to_datetime(price_data.index)

            # 3. 处理时区
            price_data = self._ensure_price_datetime_index_with_timezone(price_data)

            # 4. 标准化列名（资产名称）
            price_data = self._standardize_price_column_names(price_data)

            # 5. 处理缺失值
            price_data = self._handle_price_missing_values(price_data)

            # 6. 验证数据质量
            price_data = self._validate_price_data_quality(price_data)

            logger.info(f"Price data cleaning completed: {price_data.shape}")
            return price_data

        except Exception as e:
            logger.error(f"Error cleaning price data for analysis: {e}")
            raise

    def _ensure_factor_multiindex(self, factor_data: pd.Series) -> pd.Series:
        """确保因子数据有正确的MultiIndex格式"""
        try:
            if isinstance(factor_data.index, pd.MultiIndex):
                # 已经是MultiIndex，检查级别数
                if factor_data.index.nlevels == 2:
                    logger.debug("Factor data already has correct MultiIndex format")
                    return factor_data
                else:
                    logger.warning(f"Factor data has {factor_data.index.nlevels} levels, expected 2")
                    # 如果级别数不对，尝试重建
                    pass

            # 如果不是MultiIndex或级别数不对，尝试从索引推断结构
            logger.debug("Converting factor data to MultiIndex format")

            # 检查索引是否包含日期信息
            if hasattr(factor_data.index, 'to_series'):
                index_series = factor_data.index.to_series()
            else:
                index_series = pd.Series(factor_data.index)

            # 尝试解析索引格式
            # 常见格式：'YYYYMMDD_ASSET' 或 ('date', 'asset') 元组
            if index_series.dtype == 'object':
                # 检查是否是字符串格式
                sample_idx = str(index_series.iloc[0]) if len(index_series) > 0 else ""

                if '_' in sample_idx:
                    # 格式：'YYYYMMDD_ASSET'
                    dates = []
                    assets = []
                    for idx in index_series:
                        parts = str(idx).split('_', 1)
                        if len(parts) == 2:
                            try:
                                # 尝试解析日期，支持多种格式
                                date_str = parts[0]
                                if len(date_str) == 8:  # YYYYMMDD
                                    date_obj = pd.to_datetime(date_str, format='%Y%m%d')
                                else:
                                    date_obj = pd.to_datetime(date_str)
                                dates.append(date_obj)
                                assets.append(parts[1])
                            except (ValueError, pd.errors.ParserError):
                                # 如果日期解析失败，使用默认值
                                dates.append(pd.Timestamp.now().normalize())
                                assets.append(parts[1])
                        else:
                            # 如果格式不对，使用默认值
                            dates.append(pd.Timestamp.now().normalize())
                            assets.append(str(idx))

                    # 创建MultiIndex
                    multi_index = pd.MultiIndex.from_arrays([dates, assets], names=['date', 'asset'])
                    factor_data.index = multi_index

                    logger.debug(f"Created MultiIndex from string format: {len(dates)} records")
                    return factor_data

            # 如果无法自动解析，创建默认的MultiIndex
            logger.warning("Cannot parse index format, creating default MultiIndex")

            # 使用当前日期和序号作为默认值
            current_date = pd.Timestamp.now().normalize()
            n_records = len(factor_data)

            dates = [current_date] * n_records
            assets = [f"ASSET_{i:06d}" for i in range(n_records)]

            multi_index = pd.MultiIndex.from_arrays([dates, assets], names=['date', 'asset'])
            factor_data.index = multi_index

            logger.warning(f"Created default MultiIndex with {n_records} records")
            return factor_data

        except Exception as e:
            logger.error(f"Error ensuring factor MultiIndex: {e}")
            # 如果所有方法都失败，创建最简单的MultiIndex
            current_date = pd.Timestamp.now().normalize()
            n_records = len(factor_data)
            dates = [current_date] * n_records
            assets = [f"ASSET_{i:06d}" for i in range(n_records)]
            multi_index = pd.MultiIndex.from_arrays([dates, assets], names=['date', 'asset'])
            factor_data.index = multi_index
            logger.warning("Created fallback MultiIndex due to error")
            return factor_data

    def _ensure_factor_datetime_index_with_timezone(self, factor_data: pd.Series) -> pd.Series:
        """确保因子数据的日期索引有正确的时区"""
        try:
            date_level = factor_data.index.get_level_values(0)

            if not isinstance(date_level, pd.DatetimeIndex):
                # 转换为DatetimeIndex
                date_level = pd.to_datetime(date_level)

            # 处理时区
            if getattr(date_level, 'tz', None) is None:
                # 中国股票数据默认添加UTC时区
                date_level = date_level.tz_localize('UTC')
                self.cleaning_stats['fixed_timezones'] += 1
                logger.debug("Added UTC timezone to factor data")
            elif str(date_level.tz) != 'UTC':
                # 转换为UTC时区
                date_level = date_level.tz_convert('UTC')
                self.cleaning_stats['fixed_timezones'] += 1
                logger.debug(f"Converted timezone from {date_level.tz} to UTC")

            # 重建MultiIndex
            asset_level = factor_data.index.get_level_values(1)
            new_index = pd.MultiIndex.from_arrays([date_level, asset_level], names=['date', 'asset'])
            factor_data = pd.Series(factor_data.values, index=new_index, name=factor_data.name)

            return factor_data

        except Exception as e:
            logger.warning(f"Error ensuring factor datetime index with timezone: {e}")
            return factor_data

    def _ensure_price_datetime_index_with_timezone(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """确保价格数据的日期索引有正确的时区"""
        try:
            if isinstance(price_data.index, pd.MultiIndex):
                # 处理MultiIndex的第一级（日期级别）
                date_level = price_data.index.get_level_values(0)
                if not isinstance(date_level, pd.DatetimeIndex):
                    date_level = pd.to_datetime(date_level)

                # 处理时区
                if getattr(date_level, 'tz', None) is None:
                    # 中国股票数据默认添加UTC时区
                    date_level = date_level.tz_localize('UTC')
                    self.cleaning_stats['fixed_timezones'] += 1
                    logger.debug("Added UTC timezone to MultiIndex price data")
                elif str(date_level.tz) != 'UTC':
                    # 转换为UTC时区
                    date_level = date_level.tz_convert('UTC')
                    self.cleaning_stats['fixed_timezones'] += 1
                    logger.debug(f"Converted MultiIndex timezone from {date_level.tz} to UTC")

                # 重建MultiIndex
                new_levels = [date_level]
                new_levels.extend([price_data.index.get_level_values(i) for i in range(1, price_data.index.nlevels)])
                price_data.index = pd.MultiIndex.from_arrays(new_levels, names=price_data.index.names)

            elif isinstance(price_data.index, pd.DatetimeIndex):
                # 处理普通DatetimeIndex
                if getattr(price_data.index, 'tz', None) is None:
                    # 中国股票数据默认添加UTC时区
                    price_data.index = price_data.index.tz_localize('UTC')
                    self.cleaning_stats['fixed_timezones'] += 1
                    logger.debug("Added UTC timezone to price data")
                elif str(price_data.index.tz) != 'UTC':
                    # 转换为UTC时区
                    price_data.index = price_data.index.tz_convert('UTC')
                    self.cleaning_stats['fixed_timezones'] += 1
                    logger.debug(f"Converted timezone from {price_data.index.tz} to UTC")
            else:
                # 如果不是DatetimeIndex，先转换
                price_data.index = pd.to_datetime(price_data.index)
                price_data.index = price_data.index.tz_localize('UTC')
                self.cleaning_stats['fixed_timezones'] += 1
                logger.debug("Converted index to datetime and added UTC timezone")

            return price_data

        except Exception as e:
            logger.warning(f"Error ensuring price datetime index with timezone: {e}")
            return price_data

    def _standardize_factor_asset_names(self, factor_data: pd.Series) -> pd.Series:
        """标准化因子数据中的资产名称"""
        try:
            asset_level = factor_data.index.get_level_values(1)

            # 标准化资产名称（移除空格、统一格式等）
            standardized_assets = []
            for asset in asset_level:
                if isinstance(asset, str):
                    # 移除空格并转换为大写
                    asset = asset.strip().upper()
                    # 确保股票代码格式正确（如果需要）
                    if '.' not in asset and len(asset) == 6:
                        # 根据代码前缀添加交易所后缀
                        if asset.startswith(('000', '002', '300')):
                            asset = f"{asset}.SZ"
                        elif asset.startswith(('600', '601', '603', '688')):
                            asset = f"{asset}.SH"
                standardized_assets.append(asset)

            # 重建MultiIndex
            date_level = factor_data.index.get_level_values(0)
            new_index = pd.MultiIndex.from_arrays([date_level, standardized_assets], names=['date', 'asset'])
            factor_data = pd.Series(factor_data.values, index=new_index, name=factor_data.name)

            self.cleaning_stats['normalized_assets'] += len(set(standardized_assets))
            logger.debug(f"Standardized {len(set(standardized_assets))} unique asset names in factor data")

            return factor_data

        except Exception as e:
            logger.warning(f"Error standardizing factor asset names: {e}")
            return factor_data

    def _standardize_price_column_names(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """标准化价格数据中的列名"""
        try:
            # 对于价格数据，我们不需要标准化列名，因为列名是价格字段（open, high, low, close等）
            # 而不是资产名称。资产名称在MultiIndex的第二级。

            # 只是确保列名是小写的标准格式
            standardized_columns = []
            for col in price_data.columns:
                if isinstance(col, str):
                    # 移除空格并转换为小写
                    col = col.strip().lower()
                standardized_columns.append(col)

            price_data.columns = standardized_columns

            logger.debug(f"Standardized price column names: {standardized_columns}")

            return price_data

        except Exception as e:
            logger.warning(f"Error standardizing price column names: {e}")
            return price_data

    def _handle_factor_missing_values(self, factor_data: pd.Series) -> pd.Series:
        """处理因子数据中的缺失值"""
        try:
            missing_before = factor_data.isnull().sum()

            # 对于因子数据，通常删除缺失值而不是填充
            factor_data = factor_data.dropna()

            missing_after = missing_before - len(factor_data)
            if missing_after > 0:
                logger.debug(f"Removed {missing_after} missing values from factor data")

            return factor_data

        except Exception as e:
            logger.warning(f"Error handling factor missing values: {e}")
            return factor_data

    def _handle_price_missing_values(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """处理价格数据中的缺失值"""
        try:
            missing_before = price_data.isnull().sum().sum()

            # 对于价格数据，使用前向填充
            price_data = price_data.ffill().bfill()

            # 删除仍然为空的行
            price_data = price_data.dropna(how='all')

            missing_after = price_data.isnull().sum().sum()
            if missing_before > missing_after:
                logger.debug(f"Handled {missing_before - missing_after} missing values in price data")

            return price_data

        except Exception as e:
            logger.warning(f"Error handling price missing values: {e}")
            return price_data

    def _validate_factor_data_quality(self, factor_data: pd.Series) -> pd.Series:
        """验证因子数据质量"""
        try:
            initial_count = len(factor_data)

            # 删除无穷大值
            factor_data = factor_data.replace([np.inf, -np.inf], np.nan).dropna()

            # 删除异常值（使用3倍标准差规则）
            if len(factor_data) > 0:
                mean_val = factor_data.mean()
                std_val = factor_data.std()
                if std_val > 0:
                    lower_bound = mean_val - 3 * std_val
                    upper_bound = mean_val + 3 * std_val
                    factor_data = factor_data[(factor_data >= lower_bound) & (factor_data <= upper_bound)]

            removed_count = initial_count - len(factor_data)
            if removed_count > 0:
                logger.debug(f"Removed {removed_count} records during factor data quality validation")

            return factor_data

        except Exception as e:
            logger.warning(f"Error validating factor data quality: {e}")
            return factor_data

    def _validate_price_data_quality(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """验证价格数据质量"""
        try:
            initial_count = len(price_data)

            # 只对关键价格列进行验证，忽略可能为NaN的列（如amount）
            price_columns = ['open', 'high', 'low', 'close']
            available_price_columns = [col for col in price_columns if col in price_data.columns]

            # 删除关键价格列为0或负数的记录
            for col in available_price_columns:
                price_data = price_data[price_data[col] > 0]

            # 删除无穷大值
            price_data = price_data.replace([np.inf, -np.inf], np.nan)

            # 只删除所有关键价格列都是NaN的行
            if available_price_columns:
                price_data = price_data.dropna(subset=available_price_columns, how='all')

            removed_count = initial_count - len(price_data)
            if removed_count > 0:
                logger.debug(f"Removed {removed_count} records during price data quality validation")

            return price_data

        except Exception as e:
            logger.warning(f"Error validating price data quality: {e}")
            return price_data


# 全局数据清洗器实例
data_cleaner = DataCleaner()
