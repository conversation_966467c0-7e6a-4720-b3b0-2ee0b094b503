"""
主框架 v2

重构MainFrame，简化复杂度，提高可维护性。
遵循单一职责原则，分离界面逻辑和业务逻辑。
"""

import sys
import logging
from typing import Dict, Any
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTabWidget, QPushButton, QLabel, QMessageBox, QAction, 
    QMenuBar, QToolBar, QStatusBar, QSplitter
)
from PyQt5.QtCore import QTimer, QDateTime, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

logger = logging.getLogger(__name__)


class MainFrameV2(QMainWindow):
    """
    主框架 v2
    
    主要改进:
    - 简化初始化流程
    - 统一依赖管理
    - 模块化面板加载
    - 统一错误处理
    - 改进的状态管理
    """
    
    # 信号定义
    panel_changed = pyqtSignal(str)  # 面板切换信号
    status_updated = pyqtSignal(str)  # 状态更新信号
    
    def __init__(self, dependencies: Dict[str, Any] = None):
        """
        初始化主框架
        
        Parameters:
        -----------
        dependencies : Dict[str, Any], optional
            依赖对象字典，包含所有需要的管理器和引擎
        """
        super().__init__()
        
        # 依赖管理
        self.dependencies = dependencies or {}
        self._validate_dependencies()
        
        # 内部状态
        self.panels = {}
        self.current_panel = None
        self._timers = {}
        
        # 初始化UI
        self._init_window()
        self._init_menu_bar()
        self._init_tool_bar()
        self._init_status_bar()
        self._init_main_content()
        
        # 启动定时器
        self._start_timers()
        
        logger.info("MainFrameV2 initialized successfully")
    
    def _validate_dependencies(self):
        """验证依赖对象"""
        required_deps = [
            'account_manage', 'db_manage', 'trade_engine', 
            'position_manager', 'trade_ploter'
        ]
        
        missing_deps = [dep for dep in required_deps if dep not in self.dependencies]
        if missing_deps:
            logger.warning(f"Missing dependencies: {missing_deps}")
    
    def _init_window(self):
        """初始化窗口"""
        self.setWindowTitle("T_TRADE - 量化交易系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置窗口图标（如果有的话）
        try:
            self.setWindowIcon(QIcon("assets/icon.png"))
        except:
            pass
    
    def _init_menu_bar(self):
        """初始化菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        snapshot_action = QAction('保存账户镜像(&S)', self)
        snapshot_action.setShortcut('Ctrl+S')
        snapshot_action.triggered.connect(self._on_snapshot_account)
        tools_menu.addAction(snapshot_action)
        
        restore_action = QAction('恢复账户镜像(&R)', self)
        restore_action.setShortcut('Ctrl+R')
        restore_action.triggered.connect(self._on_restore_account)
        tools_menu.addAction(restore_action)
        
        tools_menu.addSeparator()
        
        optimize_action = QAction('数据库维护(&O)', self)
        optimize_action.triggered.connect(self._on_database_maintenance)
        tools_menu.addAction(optimize_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)
    
    def _init_tool_bar(self):
        """初始化工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        
        # 快照操作
        snapshot_action = QAction('保存镜像', self)
        snapshot_action.setToolTip('保存当前账户状态')
        snapshot_action.triggered.connect(self._on_snapshot_account)
        toolbar.addAction(snapshot_action)
        
        restore_action = QAction('恢复镜像', self)
        restore_action.setToolTip('恢复账户状态')
        restore_action.triggered.connect(self._on_restore_account)
        toolbar.addAction(restore_action)
        
        toolbar.addSeparator()
        
        # 数据刷新
        refresh_action = QAction('刷新数据', self)
        refresh_action.setToolTip('刷新实时数据')
        refresh_action.triggered.connect(self._on_refresh_data)
        toolbar.addAction(refresh_action)
    
    def _init_status_bar(self):
        """初始化状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("系统就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # 更新时间显示
        self._update_time_display()
    
    def _init_main_content(self):
        """初始化主要内容区域"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        main_layout.addWidget(self.tab_widget)
        
        # 加载面板
        self._load_panels()
    
    def _load_panels(self):
        """加载所有面板"""
        try:
            # 交易面板
            self._load_trade_panel()

            # 因子研究面板
            self._load_factor_research_panel()

            # 回测面板
            self._load_backtest_panel()

            # 设置默认面板
            if self.tab_widget.count() > 0:
                self.tab_widget.setCurrentIndex(0)

        except Exception as e:
            logger.error(f"Error loading panels: {e}")
            self._show_error(f"加载面板失败: {e}")
    
    def _load_trade_panel(self):
        """加载交易面板"""
        try:
            from gui.panels.panel_trade import PanelTrade

            trade_panel = PanelTrade(
                parent=self.tab_widget,
                position_manager=self.dependencies.get('position_manager'),
                account_manage=self.dependencies.get('account_manage'),
                trade_engine=self.dependencies.get('trade_engine'),
                db_manage=self.dependencies.get('db_manage')
            )

            self.tab_widget.addTab(trade_panel, "交易")
            self.panels['trade'] = trade_panel

        except Exception as e:
            logger.error(f"Error loading trade panel: {e}")
    
    def _load_factor_research_panel(self):
        """加载因子研究面板"""
        try:
            from gui.panels.panel_factor_research import PanelFactorResearch
            
            factor_panel = PanelFactorResearch(
                parent=self.tab_widget,
                db_manage=self.dependencies.get('db_manage')
            )
            
            self.tab_widget.addTab(factor_panel, "因子研究")
            self.panels['factor_research'] = factor_panel
            
        except Exception as e:
            logger.error(f"Error loading factor research panel: {e}")
    
    def _load_backtest_panel(self):
        """加载回测面板"""
        try:
            from gui.panels.panel_backtest import PanelBacktest
            
            backtest_panel = PanelBacktest(parent=self.tab_widget)
            
            self.tab_widget.addTab(backtest_panel, "回测")
            self.panels['backtest'] = backtest_panel
            
        except Exception as e:
            logger.error(f"Error loading backtest panel: {e}")
    
    def _start_timers(self):
        """启动定时器"""
        # 时间更新定时器
        self.time_timer = QTimer(self)
        self.time_timer.timeout.connect(self._update_time_display)
        self.time_timer.start(1000)  # 每秒更新
        
        # 状态检查定时器
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self._check_system_status)
        self.status_timer.start(30000)  # 每30秒检查
    
    # ==================== 事件处理 ====================
    
    def _on_tab_changed(self, index: int):
        """标签页切换事件"""
        if 0 <= index < self.tab_widget.count():
            panel_name = list(self.panels.keys())[index] if index < len(self.panels) else "unknown"
            self.current_panel = panel_name
            self.panel_changed.emit(panel_name)
            logger.debug(f"Panel changed to: {panel_name}")
    
    def _on_snapshot_account(self):
        """保存账户镜像"""
        try:
            account_manage = self.dependencies.get('account_manage')
            db_manage = self.dependencies.get('db_manage')

            if not account_manage or not db_manage:
                self._show_warning("账户管理或数据库管理不可用")
                return

            if not hasattr(db_manage, 'snapshot_db'):
                self._show_warning("快照功能不可用")
                return

            account_id = getattr(account_manage, '_current_account_id', None)
            if not account_id:
                self._show_info("请先选择账户")
                return

            # 获取当前账户数据
            account_data = account_manage._load_account(account_id)
            if not account_data:
                self._show_warning(f"无法获取账户 {account_id} 的数据")
                return

            # 获取账户总资产值
            total_assets = account_data.get("total_assets", 0)

            # 保存快照
            db_manage.snapshot_db.insert_snapshot(
                account_id=account_id,
                date="manual",
                value=total_assets,
                extra_json=account_data
            )

            # 显示成功信息
            self._show_info(f"账户镜像已保存\n账户: {account_id}\n总资产: {total_assets}")
            self._update_status(f"账户镜像已保存 (总资产: {total_assets})")

            logger.info(f"Account snapshot saved for {account_id} with value {total_assets}")

        except Exception as e:
            logger.error(f"Error saving account snapshot: {e}")
            self._show_error(f"保存账户镜像失败: {e}")
    
    def _on_restore_account(self):
        """恢复账户镜像"""
        try:
            account_manage = self.dependencies.get('account_manage')
            db_manage = self.dependencies.get('db_manage')

            if not account_manage or not db_manage:
                self._show_warning("账户管理或数据库管理不可用")
                return

            if not hasattr(db_manage, 'snapshot_db'):
                self._show_warning("快照功能不可用")
                return

            # 获取当前选中的账户ID
            account_id = getattr(account_manage, '_current_account_id', None)
            if not account_id:
                self._show_info("请先选择账户")
                return

            # 获取最新的快照
            snapshot = db_manage.snapshot_db.get_latest_snapshot(account_id)
            if not snapshot:
                self._show_info(f"未找到账户 {account_id} 的可恢复镜像")
                return

            account_data = snapshot.get('extra_json', {})
            if not account_data:
                self._show_warning("镜像数据为空，无法恢复")
                return

            # 确保恢复的账户数据包含正确的账户ID
            account_data['name'] = account_id

            # 保存恢复的账户数据
            account_manage._save_account(account_data)

            # 更新当前账户状态（重要：确保内存中的状态也更新）
            account_manage.set_current_account(account_id)

            # 刷新界面显示
            self._refresh_account_display()

            # 显示成功信息
            snapshot_date = snapshot.get('date', '未知')
            snapshot_value = snapshot.get('value', 0)
            self._show_info(f"账户镜像已恢复\n快照日期: {snapshot_date}\n快照价值: {snapshot_value}")
            self._update_status(f"账户镜像已恢复 (快照日期: {snapshot_date})")

            logger.info(f"Account {account_id} restored from snapshot dated {snapshot_date}")

        except Exception as e:
            logger.error(f"Error restoring account snapshot: {e}")
            self._show_error(f"恢复账户镜像失败: {e}")
    
    def _on_database_maintenance(self):
        """数据库维护"""
        try:
            db_manage = self.dependencies.get('db_manage')
            if not db_manage:
                self._show_warning("数据库管理不可用")
                return
            
            # 执行数据库维护操作
            if hasattr(db_manage, 'quant_db'):
                # 执行VACUUM操作
                db_manage.quant_db.query("VACUUM")
                # 更新统计信息
                db_manage.quant_db.query("ANALYZE")
                
                self._show_info("数据库维护完成")
                self._update_status("数据库维护完成")
            else:
                self._show_warning("数据库维护功能不可用")
                
        except Exception as e:
            logger.error(f"Error in database maintenance: {e}")
            self._show_error(f"数据库维护失败: {e}")
    
    def _on_refresh_data(self):
        """刷新数据"""
        try:
            db_manage = self.dependencies.get('db_manage')
            if not db_manage:
                self._show_warning("数据库管理不可用")
                return
            
            if hasattr(db_manage, 'update_realtime_once'):
                db_manage.update_realtime_once()
                self._show_info("数据刷新完成")
                self._update_status("数据刷新完成")
            else:
                self._show_warning("数据刷新功能不可用")
                
        except Exception as e:
            logger.error(f"Error refreshing data: {e}")
            self._show_error(f"数据刷新失败: {e}")
    
    def _on_about(self):
        """关于对话框"""
        about_text = """
        <h3>T_TRADE 量化交易系统</h3>
        <p>版本: 2.1.0</p>
        <p>一个基于Python的量化交易系统</p>
        <p>主要功能:</p>
        <ul>
        <li>因子研究和分析</li>
        <li>策略回测</li>
        <li>模拟交易</li>
        <li>实盘交易</li>
        </ul>
        """
        QMessageBox.about(self, "关于 T_TRADE", about_text)
    
    # ==================== 工具方法 ====================

    def _refresh_account_display(self):
        """刷新账户显示界面"""
        try:
            account_manage = self.dependencies.get('account_manage')
            if not account_manage:
                return

            current_account_id = getattr(account_manage, '_current_account_id', None)
            if not current_account_id:
                return

            # 刷新交易面板中的账户信息
            trade_panel = self.panels.get('trade')
            if trade_panel and hasattr(trade_panel, 'trade_tabs'):
                # 获取模拟交易面板
                for idx in range(trade_panel.trade_tabs.count()):
                    panel = trade_panel.trade_tabs.widget(idx)
                    if hasattr(panel, 'element_account'):
                        # 重新加载账户列表和信息
                        panel.element_account._load_account()

                        # 更新账户列表的选中状态
                        account_list = panel.element_account.account_list
                        for i in range(account_list.count()):
                            if account_list.item(i).text() == current_account_id:
                                account_list.setCurrentRow(i)
                                # 手动触发选择事件来更新账户信息显示
                                panel.element_account._on_select_account(current_account_id)
                                break

                        # 如果面板有on_select_account方法，也调用它来刷新股票池等信息
                        if hasattr(panel, 'on_select_account'):
                            panel.on_select_account(current_account_id)

            logger.info(f"Account display refreshed for account: {current_account_id}")

        except Exception as e:
            logger.error(f"Error refreshing account display: {e}")

    def _update_time_display(self):
        """更新时间显示"""
        current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd HH:mm:ss")
        self.time_label.setText(current_time)
    
    def _check_system_status(self):
        """检查系统状态"""
        try:
            # 这里可以添加系统状态检查逻辑
            # 例如检查数据库连接、网络状态等
            pass
        except Exception as e:
            logger.error(f"Error checking system status: {e}")
    
    def _update_status(self, message: str):
        """更新状态信息"""
        self.status_label.setText(message)
        self.status_updated.emit(message)
        logger.info(f"Status updated: {message}")
    
    def _show_error(self, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", message)
        logger.error(f"Error shown: {message}")
    
    def _show_warning(self, message: str):
        """显示警告消息"""
        QMessageBox.warning(self, "警告", message)
        logger.warning(f"Warning shown: {message}")
    
    def _show_info(self, message: str):
        """显示信息消息"""
        QMessageBox.information(self, "信息", message)
        logger.info(f"Info shown: {message}")
    
    # ==================== 生命周期管理 ====================
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 清理测试数据
            self._cleanup_test_data()
            
            # 清理面板
            for panel in self.panels.values():
                if hasattr(panel, 'cleanup'):
                    panel.cleanup()
            
            # 停止定时器
            if hasattr(self, 'time_timer'):
                self.time_timer.stop()
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()
            
            logger.info("MainFrameV2 closed successfully")
            event.accept()
            
        except Exception as e:
            logger.error(f"Error during close: {e}")
            event.accept()
    
    def _cleanup_test_data(self):
        """清理测试数据"""
        try:
            db_manage = self.dependencies.get('db_manage')
            account_manage = self.dependencies.get('account_manage')
            
            if db_manage and hasattr(db_manage, 'snapshot_db'):
                db_manage.snapshot_db.clear_test_snapshots_except_init("test")
            
            if account_manage and hasattr(account_manage, 'clear_trade_records'):
                account_manage.clear_trade_records("test")
                
        except Exception as e:
            logger.error(f"Error cleaning up test data: {e}")


# 工厂函数
def create_main_frame(dependencies: Dict[str, Any]) -> MainFrameV2:
    """
    创建主框架实例
    
    Parameters:
    -----------
    dependencies : Dict[str, Any]
        依赖对象字典
        
    Returns:
    --------
    MainFrameV2
        主框架实例
    """
    return MainFrameV2(dependencies)
