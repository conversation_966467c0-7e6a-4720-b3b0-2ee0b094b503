"""
代码质量检查工具

提供代码质量分析、SOLID原则检查、复杂度分析等功能。
帮助维护高质量的代码库。
"""

import ast
import os
import re
import inspect
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from collections import defaultdict, Counter

from .unified_logger import get_logger

logger = get_logger()


class CodeComplexityAnalyzer:
    """
    代码复杂度分析器
    
    分析代码的圈复杂度、认知复杂度等指标
    """
    
    def __init__(self):
        self.results = {}
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """分析单个文件的复杂度"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            analyzer = ComplexityVisitor()
            analyzer.visit(tree)
            
            return {
                'file_path': file_path,
                'total_lines': len(content.splitlines()),
                'functions': analyzer.functions,
                'classes': analyzer.classes,
                'complexity_score': analyzer.total_complexity,
                'max_function_complexity': max([f['complexity'] for f in analyzer.functions], default=0),
                'avg_function_complexity': sum([f['complexity'] for f in analyzer.functions]) / len(analyzer.functions) if analyzer.functions else 0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return {'file_path': file_path, 'error': str(e)}
    
    def analyze_directory(self, directory: str, extensions: List[str] = None) -> Dict[str, Any]:
        """分析目录下所有Python文件的复杂度"""
        if extensions is None:
            extensions = ['.py']
        
        results = []
        total_complexity = 0
        total_functions = 0
        total_lines = 0
        
        for root, dirs, files in os.walk(directory):
            # 跳过__pycache__目录
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)
                    result = self.analyze_file(file_path)
                    
                    if 'error' not in result:
                        results.append(result)
                        total_complexity += result['complexity_score']
                        total_functions += len(result['functions'])
                        total_lines += result['total_lines']
        
        return {
            'directory': directory,
            'files': results,
            'summary': {
                'total_files': len(results),
                'total_lines': total_lines,
                'total_functions': total_functions,
                'total_complexity': total_complexity,
                'avg_complexity_per_function': total_complexity / total_functions if total_functions > 0 else 0,
                'high_complexity_functions': self._find_high_complexity_functions(results)
            }
        }
    
    def _find_high_complexity_functions(self, results: List[Dict], threshold: int = 10) -> List[Dict]:
        """找出高复杂度函数"""
        high_complexity = []
        
        for file_result in results:
            if 'functions' in file_result:
                for func in file_result['functions']:
                    if func['complexity'] > threshold:
                        high_complexity.append({
                            'file': file_result['file_path'],
                            'function': func['name'],
                            'complexity': func['complexity'],
                            'line': func['line']
                        })
        
        return sorted(high_complexity, key=lambda x: x['complexity'], reverse=True)


class ComplexityVisitor(ast.NodeVisitor):
    """AST访问器，用于计算复杂度"""
    
    def __init__(self):
        self.functions = []
        self.classes = []
        self.current_function = None
        self.current_class = None
        self.total_complexity = 0
    
    def visit_FunctionDef(self, node):
        """访问函数定义"""
        complexity = self._calculate_complexity(node)
        
        func_info = {
            'name': node.name,
            'line': node.lineno,
            'complexity': complexity,
            'args_count': len(node.args.args),
            'lines_count': self._count_lines(node)
        }
        
        if self.current_class:
            func_info['class'] = self.current_class
        
        self.functions.append(func_info)
        self.total_complexity += complexity
        
        old_function = self.current_function
        self.current_function = node.name
        self.generic_visit(node)
        self.current_function = old_function
    
    def visit_ClassDef(self, node):
        """访问类定义"""
        class_info = {
            'name': node.name,
            'line': node.lineno,
            'methods_count': len([n for n in node.body if isinstance(n, ast.FunctionDef)]),
            'lines_count': self._count_lines(node)
        }
        
        self.classes.append(class_info)
        
        old_class = self.current_class
        self.current_class = node.name
        self.generic_visit(node)
        self.current_class = old_class
    
    def _calculate_complexity(self, node) -> int:
        """计算节点的圈复杂度"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _count_lines(self, node) -> int:
        """计算节点的行数"""
        if hasattr(node, 'end_lineno') and node.end_lineno:
            return node.end_lineno - node.lineno + 1
        return 1


class SOLIDPrincipleChecker:
    """
    SOLID原则检查器
    
    检查代码是否遵循SOLID原则
    """
    
    def __init__(self):
        self.violations = []
    
    def check_file(self, file_path: str) -> Dict[str, Any]:
        """检查文件的SOLID原则遵循情况"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            violations = []
            
            # 检查单一职责原则 (SRP)
            violations.extend(self._check_srp(tree))
            
            # 检查开闭原则 (OCP)
            violations.extend(self._check_ocp(tree))
            
            # 检查里氏替换原则 (LSP)
            violations.extend(self._check_lsp(tree))
            
            # 检查接口隔离原则 (ISP)
            violations.extend(self._check_isp(tree))
            
            # 检查依赖倒置原则 (DIP)
            violations.extend(self._check_dip(tree))
            
            return {
                'file_path': file_path,
                'violations': violations,
                'score': max(0, 100 - len(violations) * 10)  # 简单评分
            }
            
        except Exception as e:
            logger.error(f"Error checking SOLID principles for {file_path}: {e}")
            return {'file_path': file_path, 'error': str(e)}
    
    def _check_srp(self, tree) -> List[Dict]:
        """检查单一职责原则"""
        violations = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                
                # 简单检查：如果类的方法数量过多，可能违反SRP
                if len(methods) > 15:
                    violations.append({
                        'principle': 'SRP',
                        'type': 'too_many_methods',
                        'line': node.lineno,
                        'message': f"Class '{node.name}' has {len(methods)} methods, consider splitting"
                    })
        
        return violations
    
    def _check_ocp(self, tree) -> List[Dict]:
        """检查开闭原则"""
        violations = []
        
        # 检查是否有大量的if-elif-else结构，建议使用多态
        for node in ast.walk(tree):
            if isinstance(node, ast.If):
                elif_count = len([n for n in ast.walk(node) if isinstance(n, ast.If)])
                if elif_count > 5:
                    violations.append({
                        'principle': 'OCP',
                        'type': 'complex_conditional',
                        'line': node.lineno,
                        'message': f"Complex conditional structure with {elif_count} branches, consider using polymorphism"
                    })
        
        return violations
    
    def _check_lsp(self, tree) -> List[Dict]:
        """检查里氏替换原则"""
        violations = []
        
        # 检查子类是否重写了父类方法但改变了行为
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.bases:
                for method in node.body:
                    if isinstance(method, ast.FunctionDef):
                        # 检查是否有NotImplementedError
                        for stmt in ast.walk(method):
                            if (isinstance(stmt, ast.Raise) and 
                                isinstance(stmt.exc, ast.Call) and
                                isinstance(stmt.exc.func, ast.Name) and
                                stmt.exc.func.id == 'NotImplementedError'):
                                violations.append({
                                    'principle': 'LSP',
                                    'type': 'not_implemented',
                                    'line': stmt.lineno,
                                    'message': f"Method '{method.name}' raises NotImplementedError"
                                })
        
        return violations
    
    def _check_isp(self, tree) -> List[Dict]:
        """检查接口隔离原则"""
        violations = []
        
        # 检查抽象基类是否有太多抽象方法
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                abstract_methods = []
                for method in node.body:
                    if isinstance(method, ast.FunctionDef):
                        # 检查是否有@abstractmethod装饰器
                        for decorator in method.decorator_list:
                            if (isinstance(decorator, ast.Name) and 
                                decorator.id == 'abstractmethod'):
                                abstract_methods.append(method.name)
                
                if len(abstract_methods) > 10:
                    violations.append({
                        'principle': 'ISP',
                        'type': 'fat_interface',
                        'line': node.lineno,
                        'message': f"Interface '{node.name}' has {len(abstract_methods)} abstract methods, consider splitting"
                    })
        
        return violations
    
    def _check_dip(self, tree) -> List[Dict]:
        """检查依赖倒置原则"""
        violations = []
        
        # 检查是否直接实例化具体类而不是依赖抽象
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    # 简单检查：如果直接调用某些具体类的构造函数
                    concrete_classes = ['dict', 'list', 'set']  # 可以扩展
                    if node.func.id in concrete_classes:
                        continue  # 内置类型不算违反
                    
                    # 检查是否在类的__init__方法中直接实例化
                    parent = node
                    while hasattr(parent, 'parent'):
                        parent = parent.parent
                        if (isinstance(parent, ast.FunctionDef) and 
                            parent.name == '__init__'):
                            violations.append({
                                'principle': 'DIP',
                                'type': 'concrete_dependency',
                                'line': node.lineno,
                                'message': f"Direct instantiation of '{node.func.id}' in constructor"
                            })
                            break
        
        return violations


class CodeQualityChecker:
    """
    代码质量检查器主类
    
    整合所有代码质量检查功能
    """
    
    def __init__(self):
        self.complexity_analyzer = CodeComplexityAnalyzer()
        self.solid_checker = SOLIDPrincipleChecker()
    
    def analyze_project(self, project_path: str) -> Dict[str, Any]:
        """分析整个项目的代码质量"""
        logger.info(f"Starting code quality analysis for: {project_path}")
        
        # 复杂度分析
        complexity_result = self.complexity_analyzer.analyze_directory(project_path)
        
        # SOLID原则检查
        solid_violations = []
        for file_result in complexity_result['files']:
            solid_result = self.solid_checker.check_file(file_result['file_path'])
            if 'violations' in solid_result:
                solid_violations.extend(solid_result['violations'])
        
        # 生成质量报告
        quality_score = self._calculate_quality_score(complexity_result, solid_violations)
        
        report = {
            'project_path': project_path,
            'complexity_analysis': complexity_result,
            'solid_violations': solid_violations,
            'quality_score': quality_score,
            'recommendations': self._generate_recommendations(complexity_result, solid_violations)
        }
        
        logger.info(f"Code quality analysis completed. Score: {quality_score}/100")
        return report
    
    def _calculate_quality_score(self, complexity_result: Dict, solid_violations: List) -> int:
        """计算代码质量评分"""
        base_score = 100
        
        # 复杂度扣分
        high_complexity_functions = complexity_result['summary']['high_complexity_functions']
        complexity_penalty = min(len(high_complexity_functions) * 5, 30)
        
        # SOLID违反扣分
        solid_penalty = min(len(solid_violations) * 2, 20)
        
        # 平均复杂度扣分
        avg_complexity = complexity_result['summary']['avg_complexity_per_function']
        if avg_complexity > 10:
            avg_complexity_penalty = min((avg_complexity - 10) * 2, 15)
        else:
            avg_complexity_penalty = 0
        
        final_score = base_score - complexity_penalty - solid_penalty - avg_complexity_penalty
        return max(0, int(final_score))
    
    def _generate_recommendations(self, complexity_result: Dict, solid_violations: List) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 复杂度建议
        high_complexity = complexity_result['summary']['high_complexity_functions']
        if high_complexity:
            recommendations.append(f"Consider refactoring {len(high_complexity)} high-complexity functions")
            for func in high_complexity[:3]:  # 只显示前3个
                recommendations.append(f"  - {func['function']} in {func['file']} (complexity: {func['complexity']})")
        
        # SOLID原则建议
        solid_by_principle = defaultdict(int)
        for violation in solid_violations:
            solid_by_principle[violation['principle']] += 1
        
        for principle, count in solid_by_principle.items():
            recommendations.append(f"Address {count} {principle} violations")
        
        # 通用建议
        avg_complexity = complexity_result['summary']['avg_complexity_per_function']
        if avg_complexity > 8:
            recommendations.append("Consider breaking down complex functions into smaller ones")
        
        if not recommendations:
            recommendations.append("Code quality looks good! Keep up the good work.")
        
        return recommendations


def analyze_code_quality(project_path: str = ".") -> Dict[str, Any]:
    """分析代码质量的便捷函数"""
    checker = CodeQualityChecker()
    return checker.analyze_project(project_path)
