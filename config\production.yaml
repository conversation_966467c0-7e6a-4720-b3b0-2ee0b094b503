# 生产环境配置
database:
  path: "data/quant_data.duckdb"
  cache_size: 1000
  connection_timeout: 30
  backup_enabled: true
  backup_interval: 1800  # 30分钟

factor_research:
  factor_calculation:
    parallel_enabled: true
    max_workers: 4
  visualization:
    dpi: 300
    save_format: "png"

trading:
  data_update:
    realtime_interval: 5  # 生产环境更频繁更新

logging:
  level: "INFO"
  handlers:
    console:
      level: "WARNING"
    file:
      level: "INFO"

performance:
  monitoring:
    enabled: true
    alert_thresholds:
      memory_usage: 0.8
      cpu_usage: 0.9
      response_time: 3.0
      error_rate: 0.02
  caching:
    memory_cache_size: "500MB"
    disk_cache_size: "2GB"
