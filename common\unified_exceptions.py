"""
统一异常处理系统

定义项目中的所有异常类型，提供统一的错误处理机制。
遵循异常处理最佳实践，提供详细的错误信息和上下文。
"""

import traceback
import functools
from typing import Dict, Any, Optional, Callable, Type
from datetime import datetime
from .unified_logger import get_logger

logger = get_logger()


# ==================== 基础异常类 ====================

class TTradeException(Exception):
    """T_TRADE项目基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, context: Dict[str, Any] = None):
        """
        初始化异常
        
        Parameters:
        -----------
        message : str
            错误消息
        error_code : str, optional
            错误代码
        context : Dict[str, Any], optional
            错误上下文信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.context = context or {}
        self.timestamp = datetime.now()
        self.traceback_str = traceback.format_exc()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_type': self.__class__.__name__,
            'error_code': self.error_code,
            'message': self.message,
            'context': self.context,
            'timestamp': self.timestamp.isoformat(),
            'traceback': self.traceback_str
        }
    
    def __str__(self):
        context_str = f" | Context: {self.context}" if self.context else ""
        return f"[{self.error_code}] {self.message}{context_str}"


# ==================== 数据相关异常 ====================

class DataException(TTradeException):
    """数据相关异常基类"""
    pass


class DataNotFoundError(DataException):
    """数据未找到异常"""
    pass


class DataFormatError(DataException):
    """数据格式错误异常"""
    pass


class DataValidationError(DataException):
    """数据验证错误异常"""
    pass


class DatabaseConnectionError(DataException):
    """数据库连接错误异常"""
    pass


class DatabaseOperationError(DataException):
    """数据库操作错误异常"""
    pass


# ==================== 因子研究相关异常 ====================

class FactorException(TTradeException):
    """因子研究相关异常基类"""
    pass


class FactorCalculationError(FactorException):
    """因子计算错误异常"""
    pass


class FactorAnalysisError(FactorException):
    """因子分析错误异常"""
    pass


class FactorDataError(FactorException):
    """因子数据错误异常"""
    pass


class AlphalensError(FactorException):
    """Alphalens相关错误异常"""
    pass


# ==================== 交易相关异常 ====================

class TradingException(TTradeException):
    """交易相关异常基类"""
    pass


class OrderError(TradingException):
    """订单错误异常"""
    pass


class PositionError(TradingException):
    """持仓错误异常"""
    pass


class RiskControlError(TradingException):
    """风控错误异常"""
    pass


class AccountError(TradingException):
    """账户错误异常"""
    pass


# ==================== GUI相关异常 ====================

class GUIException(TTradeException):
    """GUI相关异常基类"""
    pass


class PanelError(GUIException):
    """面板错误异常"""
    pass


class VisualizationError(GUIException):
    """可视化错误异常"""
    pass


# ==================== 配置相关异常 ====================

class ConfigException(TTradeException):
    """配置相关异常基类"""
    pass


class ConfigLoadError(ConfigException):
    """配置加载错误异常"""
    pass


class ConfigValidationError(ConfigException):
    """配置验证错误异常"""
    pass


# ==================== 网络相关异常 ====================

class NetworkException(TTradeException):
    """网络相关异常基类"""
    pass


class APIError(NetworkException):
    """API错误异常"""
    pass


class TimeoutError(NetworkException):
    """超时错误异常"""
    pass


class RateLimitError(NetworkException):
    """频率限制错误异常"""
    pass


# ==================== 异常处理装饰器 ====================

def handle_exceptions(
    default_return=None,
    exceptions: tuple = (Exception,),
    log_level: str = "error",
    reraise: bool = False,
    context_func: Callable = None
):
    """
    异常处理装饰器
    
    Parameters:
    -----------
    default_return : Any
        异常时的默认返回值
    exceptions : tuple
        要捕获的异常类型
    log_level : str
        日志级别
    reraise : bool
        是否重新抛出异常
    context_func : Callable
        获取上下文信息的函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                # 获取上下文信息
                context = {}
                if context_func:
                    try:
                        context = context_func(*args, **kwargs)
                    except:
                        pass
                
                # 记录日志
                log_func = getattr(logger, log_level, logger.error)
                log_func(f"Exception in {func.__name__}: {str(e)}", extra={'context': context})
                
                # 是否重新抛出
                if reraise:
                    raise
                
                return default_return
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default=None, **kwargs):
    """
    安全执行函数
    
    Parameters:
    -----------
    func : Callable
        要执行的函数
    *args
        函数参数
    default : Any
        异常时的默认返回值
    **kwargs
        函数关键字参数
        
    Returns:
    --------
    Any
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Error in safe_execute: {str(e)}")
        return default


# ==================== 异常管理器 ====================

class ExceptionManager:
    """
    异常管理器
    
    提供异常统计、分析和报告功能
    """
    
    def __init__(self):
        self.exceptions = []
        self.stats = {
            'total_count': 0,
            'by_type': {},
            'by_code': {},
            'recent_errors': []
        }
    
    def record_exception(self, exception: Exception, context: Dict[str, Any] = None):
        """记录异常"""
        try:
            # 创建异常记录
            if isinstance(exception, TTradeException):
                exc_record = exception.to_dict()
            else:
                exc_record = {
                    'error_type': exception.__class__.__name__,
                    'error_code': exception.__class__.__name__,
                    'message': str(exception),
                    'context': context or {},
                    'timestamp': datetime.now().isoformat(),
                    'traceback': traceback.format_exc()
                }
            
            # 添加到记录列表
            self.exceptions.append(exc_record)
            
            # 更新统计信息
            self._update_stats(exc_record)
            
            # 记录日志
            logger.error(f"Exception recorded: {exc_record['error_type']} - {exc_record['message']}")
            
        except Exception as e:
            logger.error(f"Error recording exception: {str(e)}")
    
    def _update_stats(self, exc_record: Dict[str, Any]):
        """更新统计信息"""
        self.stats['total_count'] += 1
        
        # 按类型统计
        error_type = exc_record['error_type']
        self.stats['by_type'][error_type] = self.stats['by_type'].get(error_type, 0) + 1
        
        # 按错误代码统计
        error_code = exc_record['error_code']
        self.stats['by_code'][error_code] = self.stats['by_code'].get(error_code, 0) + 1
        
        # 最近错误（保留最近10个）
        self.stats['recent_errors'].append(exc_record)
        if len(self.stats['recent_errors']) > 10:
            self.stats['recent_errors'].pop(0)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取异常统计信息"""
        return self.stats.copy()
    
    def get_recent_exceptions(self, count: int = 10) -> list:
        """获取最近的异常"""
        return self.exceptions[-count:] if self.exceptions else []
    
    def clear_exceptions(self):
        """清除异常记录"""
        self.exceptions.clear()
        self.stats = {
            'total_count': 0,
            'by_type': {},
            'by_code': {},
            'recent_errors': []
        }
        logger.info("Exception records cleared")
    
    def export_exceptions(self, file_path: str):
        """导出异常记录到文件"""
        try:
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'stats': self.stats,
                    'exceptions': self.exceptions
                }, f, indent=2, ensure_ascii=False)
            logger.info(f"Exceptions exported to: {file_path}")
        except Exception as e:
            logger.error(f"Error exporting exceptions: {str(e)}")


# 全局异常管理器
_global_exception_manager = ExceptionManager()


def get_exception_manager() -> ExceptionManager:
    """获取全局异常管理器"""
    return _global_exception_manager


def record_exception(exception: Exception, context: Dict[str, Any] = None):
    """记录异常到全局管理器"""
    _global_exception_manager.record_exception(exception, context)


# ==================== 上下文管理器 ====================

class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(self, operation: str, **context):
        self.operation = operation
        self.context = context
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        logger.debug(f"Starting operation: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is not None:
            # 发生异常
            context = {
                **self.context,
                'operation': self.operation,
                'duration': duration
            }
            record_exception(exc_val, context)
            logger.error(f"Operation failed: {self.operation} after {duration:.3f}s")
        else:
            # 正常完成
            logger.debug(f"Operation completed: {self.operation} in {duration:.3f}s")
        
        return False  # 不抑制异常


# ==================== 便捷函数 ====================

def create_error(error_class: Type[TTradeException], message: str, 
                error_code: str = None, **context) -> TTradeException:
    """创建错误实例"""
    return error_class(message, error_code, context)


def raise_error(error_class: Type[TTradeException], message: str, 
               error_code: str = None, **context):
    """抛出错误"""
    raise create_error(error_class, message, error_code, **context)


def validate_data(data, validator_func: Callable, error_message: str = "Data validation failed"):
    """验证数据"""
    if not validator_func(data):
        raise DataValidationError(error_message, context={'data_type': type(data).__name__})
