"""
因子分析引擎 - 整合版本

整合了alphalens_engine、analysis_engine、analyzer的功能，
提供统一的因子分析接口，职责清晰，功能完整。

主要功能:
- 因子有效性分析（基于alphalens）
- 数据格式转换和预处理
- 分析结果处理和汇总
- 简化分析降级方案
- 缓存和性能管理
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Any

from .data_adapter import DataAdapter

logger = logging.getLogger(__name__)

# 尝试导入alphalens
try:
    import alphalens as al
    ALPHALENS_AVAILABLE = True
    logger.info("Alphalens imported successfully")
except ImportError:
    ALPHALENS_AVAILABLE = False
    logger.warning("Alphalens not available. Some analysis features will be limited.")


class FactorAnalyzer:
    """
    因子分析引擎
    
    整合了所有因子分析相关功能，提供统一的分析接口。
    """
    
    def __init__(self, db_manage, data_engine=None):
        """
        初始化因子分析引擎
        
        Parameters:
        -----------
        db_manage : DBManage
            数据库管理对象
        data_engine : DataEngine, optional
            数据引擎对象，如果不提供则自动创建
        """
        # 初始化数据适配器
        self.data_adapter = DataAdapter(db_manage, data_engine)
        self.available = ALPHALENS_AVAILABLE
        
        # 分析统计
        self._analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'simplified_analyses': 0
        }
        
        if not self.available:
            logger.warning("Alphalens not available. Using simplified analysis methods.")
        else:
            logger.info("FactorAnalyzer initialized with full alphalens support.")
    
    def analyze_factor(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20],
                      quantiles: int = 5, max_loss: float = 0.35,
                      groupby_data: pd.Series = None) -> Dict[str, Any]:
        """
        完整的因子分析

        Parameters:
        -----------
        factor_data : pd.Series
            因子数据，MultiIndex (date, asset)，已清洗
        periods : List[int], optional
            持有期列表，默认 [1, 5, 10, 20]
        quantiles : int, optional
            分位数数量，默认5
        max_loss : float, optional
            最大数据丢失比例，默认0.35
        groupby_data : pd.Series, optional
            分组数据，如行业分类

        Returns:
        --------
        Dict[str, Any]
            分析结果字典
        """
        try:
            self._analysis_stats['total_analyses'] += 1
            logger.info("Starting factor analysis...")



            # 检查因子数据是否为空
            if len(factor_data) == 0:
                logger.warning("Factor data is empty, returning simplified analysis")
                return self._simplified_analysis(factor_data)

            if not self.available:
                logger.warning("Alphalens not available, using simplified analysis")
                return self._simplified_analysis(factor_data)

            # 1. 获取价格数据
            pricing_data = self._get_pricing_data(factor_data)

            # 检查价格数据是否为空
            if pricing_data.empty:
                logger.warning("No pricing data available, falling back to simplified analysis")
                return self._simplified_analysis(factor_data)

            # 2. 转换为alphalens格式
            factor_data_alphalens = self._convert_to_alphalens_format(
                factor_data, pricing_data, periods, quantiles, max_loss
            )

            # 检查是否返回了简化分析结果
            if isinstance(factor_data_alphalens, dict):
                return factor_data_alphalens
            
            # 3. 执行各种分析
            results = {}
            
            # IC分析
            results.update(self._ic_analysis(factor_data_alphalens))
            
            # 分位数分析
            results.update(self._quantile_analysis(factor_data_alphalens))
            
            # 换手率分析
            results.update(self._turnover_analysis(factor_data_alphalens))
            
            # 因子收益分析
            results.update(self._factor_returns_analysis(factor_data_alphalens))
            
            # 分组分析（如果有分组数据）
            if groupby_data is not None:
                results.update(self._group_analysis(factor_data_alphalens, groupby_data))
            
            # 生成汇总统计
            results.update(self._generate_summary_stats(results))
            
            self._analysis_stats['successful_analyses'] += 1
            logger.info("Factor analysis completed successfully")
            return results
            
        except Exception as e:
            self._analysis_stats['failed_analyses'] += 1
            logger.error(f"Error in factor analysis: {e}")
            # 根据用户偏好，alphalens错误时直接退出
            raise
    
    def _get_pricing_data(self, factor_data: pd.Series) -> pd.DataFrame:
        """获取价格数据"""
        try:
            # 检查因子数据索引格式
            if not isinstance(factor_data.index, pd.MultiIndex):
                raise ValueError("Factor data must have MultiIndex (date, asset). "
                               f"Current index type: {type(factor_data.index)}")

            if factor_data.index.nlevels != 2:
                raise ValueError(f"Factor data index must have exactly 2 levels (date, asset). "
                               f"Current levels: {factor_data.index.nlevels}")

            # 从因子数据中提取资产和日期范围
            assets = factor_data.index.get_level_values(1).unique().tolist()

            # 检查是否有有效的日期数据
            date_level = factor_data.index.get_level_values(0)

            if len(date_level) == 0 or pd.isna(date_level).all():
                logger.warning("No valid dates in factor data, using default date range")
                start_date = pd.Timestamp.now().normalize()
                end_date = start_date + pd.Timedelta(days=1)
            else:
                start_date = date_level.min()
                end_date = date_level.max()

                # 检查日期是否为NaT
                if pd.isna(start_date) or pd.isna(end_date):
                    logger.warning("Invalid dates in factor data, using default date range")
                    start_date = pd.Timestamp.now().normalize()
                    end_date = start_date + pd.Timedelta(days=1)

            # 扩展日期范围以获取足够的价格数据
            extended_start = start_date - pd.Timedelta(days=30)
            extended_end = end_date + pd.Timedelta(days=30)

            # 从data_adapter获取已清洗的价格数据
            pricing_data = self.data_adapter.get_price_data(
                assets=assets,
                start_date=extended_start.strftime('%Y-%m-%d'),
                end_date=extended_end.strftime('%Y-%m-%d'),
                fields=['close']
            )



            # 确保返回DataFrame格式
            if isinstance(pricing_data, pd.DataFrame):
                if pricing_data.empty:
                    logger.warning("Pricing data is empty")
                    return pd.DataFrame()
                elif 'close' in pricing_data.columns:
                    return pricing_data[['close']]
                elif len(pricing_data.columns) > 0:
                    # 如果没有close列但有其他列，使用第一列
                    return pricing_data.iloc[:, [0]]
                else:
                    logger.warning("Pricing data has no columns")
                    return pd.DataFrame()
            else:
                raise ValueError(f"Expected DataFrame, got {type(pricing_data)}")

        except Exception as e:
            logger.error(f"Error getting pricing data: {e}")
            raise
    
    def _convert_to_alphalens_format(self, factor_data: pd.Series, pricing_data: pd.DataFrame,
                                   periods: List[int], quantiles: int, max_loss: float) -> pd.DataFrame:
        """转换为alphalens期望的格式"""
        try:
            if not self.available:
                raise ValueError("Alphalens not available")
            
            logger.debug("Converting data to alphalens format...")
            
            # 确保价格数据是正确的格式（日期为索引，资产为列）
            if isinstance(pricing_data, pd.DataFrame) and len(pricing_data.columns) == 1:
                # 如果只有一列，需要转换为多列格式
                pricing_series = pricing_data.iloc[:, 0]
                if isinstance(pricing_series.index, pd.MultiIndex):
                    pricing_data = pricing_series.unstack(level=1)
                else:
                    # 如果不是MultiIndex，假设已经是正确格式
                    pass
            
            # 使用alphalens转换
            factor_data_alphalens = al.utils.get_clean_factor_and_forward_returns(
                factor=factor_data,
                prices=pricing_data,
                periods=periods,
                quantiles=quantiles,
                max_loss=max_loss
            )
            
            logger.debug(f"Alphalens conversion completed: {factor_data_alphalens.shape}")
            return factor_data_alphalens
            
        except Exception as e:
            logger.error(f"Error converting to alphalens format: {e}")
            
            # 尝试降级处理
            if "integer" in str(e) and "NaN" in str(e):
                logger.debug("Attempting fallback with simplified quantiles...")
                try:
                    fallback_quantiles = 2
                    factor_data_alphalens = al.utils.get_clean_factor_and_forward_returns(
                        factor=factor_data,
                        prices=pricing_data,
                        periods=periods,
                        quantiles=fallback_quantiles,
                        max_loss=0.8  # 更宽松的max_loss
                    )
                    logger.debug(f"Fallback alphalens conversion completed: {factor_data_alphalens.shape}")
                    return factor_data_alphalens
                except Exception as fallback_e:
                    logger.error(f"Fallback alphalens conversion also failed: {fallback_e}")
                    # 如果所有alphalens方法都失败，使用简化分析
                    logger.warning("All alphalens methods failed, using simplified analysis")
                    return self._simplified_analysis(factor_data)
            else:
                # 检查是否是数据丢失过多的错误
                if "max_loss" in str(e) and "exceeded" in str(e):
                    logger.warning("Data loss too high for alphalens, using simplified analysis")
                    return self._simplified_analysis(factor_data)
                else:
                    raise e
    
    def _simplified_analysis(self, factor_data: pd.Series) -> Dict[str, Any]:
        """简化分析（当alphalens不可用时）"""
        self._analysis_stats['simplified_analyses'] += 1
        logger.info("Performing simplified factor analysis...")

        try:
            # 检查是否为空数据
            if len(factor_data) == 0:
                logger.warning("Empty factor data, returning minimal analysis")
                return {
                    'basic_stats': pd.Series({
                        'count': 0,
                        'mean': 0.0,
                        'std': 0.0,
                        'min': 0.0,
                        'max': 0.0,
                        'skew': 0.0,
                        'kurt': 0.0
                    }),
                    'simplified_analysis': True,
                    'analysis_type': 'simplified_empty',
                    'message': 'No factor data available for analysis'
                }

            # 基础统计
            basic_stats = {
                'count': len(factor_data),
                'mean': factor_data.mean(),
                'std': factor_data.std(),
                'min': factor_data.min(),
                'max': factor_data.max(),
                'skew': factor_data.skew(),
                'kurt': factor_data.kurtosis()
            }

            return {
                'basic_stats': pd.Series(basic_stats),
                'simplified_analysis': True,
                'analysis_type': 'simplified'
            }

        except Exception as e:
            logger.error(f"Error in simplified analysis: {e}")
            return {'error': str(e)}

    def _ic_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """IC分析"""
        try:
            if not self.available:
                return {}

            logger.debug("Performing IC analysis using alphalens...")

            # IC时序
            ic_data = al.performance.factor_information_coefficient(factor_data_clean)

            # IC统计
            ic_summary = al.performance.factor_information_coefficient(factor_data_clean).describe()

            return {
                'ic_data': ic_data,
                'ic_summary': ic_summary
            }

        except Exception as e:
            logger.error(f"Error in IC analysis: {e}")
            return {}

    def _quantile_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """分位数分析"""
        try:
            if not self.available:
                return {}

            logger.debug("Performing quantile analysis using alphalens...")

            # 分位数收益
            quantile_returns = al.performance.mean_return_by_quantile(factor_data_clean)

            # 多空收益 - 修复参数问题
            try:
                # 尝试新版本的API
                long_short_returns = al.performance.compute_mean_returns_spread(
                    quantile_returns[0],
                    upper_quant=quantile_returns[0].index.max(),
                    lower_quant=quantile_returns[0].index.min()
                )
            except TypeError:
                # 如果参数不对，使用简化计算
                mean_returns = quantile_returns[0]
                if len(mean_returns.index) >= 2:
                    long_short_returns = mean_returns.loc[mean_returns.index.max()] - mean_returns.loc[mean_returns.index.min()]
                else:
                    long_short_returns = pd.DataFrame()

            return {
                'quantile_returns': quantile_returns[0],
                'quantile_returns_std': quantile_returns[1],
                'long_short_returns': long_short_returns
            }

        except Exception as e:
            logger.error(f"Error in quantile analysis: {e}")
            return {}

    def _turnover_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """换手率分析"""
        try:
            if not self.available:
                return {}

            logger.debug("Performing turnover analysis using alphalens...")

            # 分位数换手率 - 修复参数问题
            try:
                # 尝试不同的API调用方式
                turnover_data = al.performance.quantile_turnover(
                    factor_data_clean,
                    quantile=None  # 让alphalens自动处理
                )
            except TypeError:
                # 如果参数不对，跳过换手率分析
                logger.warning("Skipping turnover analysis due to API compatibility issues")
                turnover_data = pd.DataFrame()

            # 因子自相关
            try:
                autocorr_data = al.performance.factor_rank_autocorrelation(factor_data_clean)
            except Exception:
                logger.warning("Skipping autocorrelation analysis due to API compatibility issues")
                autocorr_data = pd.DataFrame()

            return {
                'turnover_data': turnover_data,
                'autocorr_data': autocorr_data
            }

        except Exception as e:
            logger.error(f"Error in turnover analysis: {e}")
            return {}

    def _factor_returns_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """因子收益分析"""
        try:
            if not self.available:
                return {}

            logger.debug("Performing factor returns analysis using alphalens...")

            # 因子收益
            factor_returns = al.performance.factor_returns(factor_data_clean)

            # 累积因子收益
            cumulative_returns = al.performance.cumulative_returns(factor_returns)

            return {
                'factor_returns': factor_returns,
                'cumulative_returns': cumulative_returns
            }

        except Exception as e:
            logger.error(f"Error in factor returns analysis: {e}")
            return {}

    def _group_analysis(self, factor_data_clean: pd.DataFrame, groupby_data: pd.Series) -> Dict[str, pd.DataFrame]:
        """分组分析"""
        try:
            if not self.available:
                return {}

            logger.debug("Performing group analysis using alphalens...")

            # 按组分析IC
            ic_by_group = al.performance.mean_information_coefficient(
                factor_data_clean, group_neutral=True
            )

            # 按组分析收益
            returns_by_group = al.performance.mean_return_by_quantile(
                factor_data_clean, by_group=True
            )[0]

            return {
                'ic_by_group': ic_by_group,
                'returns_by_group': returns_by_group
            }

        except Exception as e:
            logger.error(f"Error in group analysis: {e}")
            return {}

    def _generate_summary_stats(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总统计"""
        try:
            summary = {}

            # IC统计
            if 'ic_summary' in results:
                ic_summary = results['ic_summary']
                if not ic_summary.empty:
                    summary['ic_mean'] = ic_summary.loc['mean'].iloc[0] if 'mean' in ic_summary.index else None
                    summary['ic_std'] = ic_summary.loc['std'].iloc[0] if 'std' in ic_summary.index else None
                    summary['ic_ir'] = summary['ic_mean'] / summary['ic_std'] if summary.get('ic_std', 0) != 0 else None

            # 收益统计
            if 'quantile_returns' in results:
                mean_returns = results['quantile_returns']
                if not mean_returns.empty:
                    # 计算多空收益（最高分位数 - 最低分位数）
                    if len(mean_returns.index) >= 2:
                        long_short_returns = mean_returns.iloc[-1] - mean_returns.iloc[0]
                        summary['long_short_returns'] = long_short_returns.mean() if hasattr(long_short_returns, 'mean') else long_short_returns

            # 换手率统计
            if 'turnover_data' in results:
                turnover = results['turnover_data']
                if not turnover.empty:
                    summary['avg_turnover'] = turnover.mean().mean() if hasattr(turnover.mean(), 'mean') else turnover.mean()

            return {'summary_stats': summary}

        except Exception as e:
            logger.error(f"Error generating summary stats: {e}")
            return {}

    # 向后兼容方法
    def factor_decay_analysis(self, factor_data: pd.Series, periods: List[int] = None) -> Dict[str, pd.DataFrame]:
        """因子衰减分析"""
        if periods is None:
            periods = list(range(1, 21))  # 1-20天

        logger.info("Performing factor decay analysis")
        return self.analyze_factor(factor_data, periods=periods)

    def compare_factors(self, factor_data_dict: Dict[str, pd.Series],
                       periods: List[int] = [1, 5, 10, 20]) -> Dict[str, pd.DataFrame]:
        """比较多个因子的表现"""
        try:
            logger.info(f"Comparing {len(factor_data_dict)} factors")

            comparison_results = {}
            for factor_name, factor_data in factor_data_dict.items():
                logger.info(f"Analyzing factor: {factor_name}")
                results = self.analyze_factor(factor_data, periods=periods)
                comparison_results[factor_name] = results

            # 汇总比较
            ic_comparison = pd.DataFrame()
            summary_comparison = pd.DataFrame()

            for factor_name, results in comparison_results.items():
                if 'ic_data' in results and not results['ic_data'].empty:
                    ic_data = results['ic_data']
                    ic_comparison[factor_name] = ic_data.iloc[:, 0] if len(ic_data.columns) > 0 else pd.Series()

                if 'summary_stats' in results:
                    summary_stats = results['summary_stats']
                    summary_comparison[factor_name] = pd.Series(summary_stats)

            return {
                'ic_comparison': ic_comparison,
                'summary_comparison': summary_comparison,
                'individual_results': comparison_results
            }

        except Exception as e:
            logger.error(f"Error in factor comparison: {e}")
            return {}

    # 数据适配器相关方法
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        return self.data_adapter.get_cache_stats()

    def clear_cache(self):
        """清空缓存"""
        self.data_adapter.clear_cache()

    def get_data_engine_stats(self) -> Dict:
        """获取数据引擎的清洗统计信息"""
        return self.data_adapter.get_data_engine_stats()

    def get_analysis_stats(self) -> Dict[str, int]:
        """获取分析统计信息"""
        return self._analysis_stats.copy()

    @property
    def available_features(self) -> Dict[str, bool]:
        """获取可用功能列表"""
        return {
            'alphalens': self.available,
            'full_analysis': self.available,
            'simplified_analysis': True,
            'data_adapter': True,
            'cache_management': True,
            'factor_comparison': True,
            'decay_analysis': True
        }
