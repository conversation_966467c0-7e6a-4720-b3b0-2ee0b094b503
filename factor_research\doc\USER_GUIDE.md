# 因子研究框架用户指南

## 🚀 快速开始

### 安装依赖

```bash
pip install pandas numpy alphalens matplotlib seaborn pyyaml scipy
```

### 基础使用

```python
from factor_research import FactorEngine, FactorAnalyzer, DataAdapter

# 初始化数据适配器
data_adapter = DataAdapter(db_manage=your_db_manage)

# 初始化因子计算引擎
factor_engine = FactorEngine(data_adapter=data_adapter)

# 计算因子
factor_data = factor_engine.calculate_factor(
    factor_name="momentum_20d",
    assets=["000001", "000002"],
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# 初始化因子分析器
analyzer = FactorAnalyzer(db_manage=your_db_manage)

# 分析因子
results = analyzer.analyze_factor(factor_data)

# 可视化结果
from factor_research import FactorVisualizer
visualizer = FactorVisualizer()
visualizer.plot_factor_analysis(results)
```

## 📊 支持的因子类型

### 动量因子
- **MomentumFactor**: 价格动量因子
- **RSIFactor**: 相对强弱指数
- **PriceVolumeTrendFactor**: 价量趋势因子

### 自定义因子

```python
from factor_research.factors import BaseFactor

class MyCustomFactor(BaseFactor):
    def calculate(self, data, **params):
        # 实现你的因子计算逻辑
        return factor_series
    
    def get_required_data(self):
        return ['close', 'volume']

# 注册自定义因子
factor_engine.register_factor(MyCustomFactor)
```

## 🔧 配置管理

```python
from factor_research.utils.config import FactorConfig

# 创建配置
config = FactorConfig()

# 修改配置
config.set('alphalens_settings.periods', [1, 5, 10, 20])
config.set('visualization.dpi', 300)

# 保存配置
config.save_to_file('my_config.yaml')

# 使用配置初始化框架
config = FactorConfig.load_from_file('my_config.yaml')
data_adapter = DataAdapter(db_manage=your_db_manage)
factor_engine = FactorEngine(data_adapter=data_adapter)
analyzer = FactorAnalyzer(db_manage=your_db_manage)
```

## 📈 分析功能

### IC分析
- IC时序分析
- IC分布统计
- IC累计图表
- IC信息比率

### 分位数分析
- 分位数收益统计
- 多空收益分析
- 收益分布热力图
- 累计收益曲线

### 换手率分析
- 分位数换手率
- 因子自相关分析
- 换手率时序图

## 🎯 批量分析

```python
# 批量计算多个因子
factor_data_dict = factor_engine.calculate_multiple_factors(
    factor_names=["momentum_20d", "rsi_14d"],
    assets=asset_list,
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# 批量分析因子
results = {}
for factor_name, factor_data in factor_data_dict.items():
    results[factor_name] = analyzer.analyze_factor(factor_data)

# 因子比较
comparison = research.compare_factors(factor_data_dict)
```

## 📊 可视化

框架自动生成各种分析图表：
- IC时序图和分布图
- 分位数收益图
- 累计收益曲线
- 因子分布分析
- 因子比较雷达图

## 🔍 示例代码

查看 `examples/basic_usage.py` 获取完整示例。

## 📝 注意事项

1. 确保数据格式为MultiIndex (date, asset)
2. 因子计算需要足够的历史数据
3. 建议启用缓存以提高性能
4. 生产环境中注意内存使用
