import duckdb
import pandas as pd
import os
import logging
from datetime import datetime
from typing import Optional, List
from common.config import DATA_BASE_DIR  # 新增导入
from .base_db import BaseDB

logger = logging.getLogger(__name__)


class QuantDB(BaseDB):
    """
    量化数据库类

    继承BaseDB，提供量化交易相关的数据存储和查询功能。
    支持股票、ETF、期货等多种金融工具的数据管理。
    """

    def __init__(self, db_path=None):
        """
        初始化量化数据库

        Parameters:
        -----------
        db_path : str, optional
            数据库文件路径，默认使用配置中的路径
        """
        # 设置数据库路径
        if db_path is None:
            db_path = os.path.join(DATA_BASE_DIR, "quant_data.duckdb")

        # 调用父类初始化
        super().__init__(db_path=db_path, read_only=False)

    def _get_default_db_path(self) -> str:
        """获取默认数据库路径"""
        return os.path.join(DATA_BASE_DIR, "quant_data.duckdb")

    def _init_tables(self):
        """初始化量化数据相关的表结构"""
        # 这里可以添加初始化表结构的逻辑
        # 目前保持向后兼容，不强制创建表
        pass

    # ==================== 量化数据专用方法 ====================

    def get_table_name(self, category: str, freq: str) -> str:
        """
        生成标准表名，如: stock_daily, indexfuture_240min, industry_weekly, market_monthly

        Parameters:
        -----------
        category : str
            数据分类: 'stock'/'indexfuture'/'industry'/'market'
        freq : str
            数据频率: '240min'/'daily'/'weekly'/'monthly'

        Returns:
        --------
        str
            标准表名
        """
        return f"{category.lower()}_{freq.lower()}"

    def save_data(self, category: str, freq: str, df: pd.DataFrame, if_exists: str = "replace") -> bool:
        """
        保存数据到指定分类和周期的表

        Parameters:
        -----------
        category : str
            数据分类
        freq : str
            数据频率
        df : pd.DataFrame
            要保存的数据
        if_exists : str
            如果表存在的处理方式

        Returns:
        --------
        bool
            是否保存成功
        """
        try:
            table_name = self.get_table_name(category, freq)
            return self.create_table_from_df(table_name, df, if_exists=if_exists)
        except Exception as e:
            logger.error(f"Error saving data to {category}_{freq}: {e}")
            return False

    def load_data(self, category: str, freq: str, where: str = None) -> pd.DataFrame:
        """
        读取指定分类和周期的数据

        Parameters:
        -----------
        category : str
            数据分类
        freq : str
            数据频率
        where : str, optional
            WHERE条件

        Returns:
        --------
        pd.DataFrame
            查询结果
        """
        try:
            table_name = self.get_table_name(category, freq)
            return self.read_table(table_name, where=where)
        except Exception as e:
            logger.error(f"Error loading data from {category}_{freq}: {e}")
            return pd.DataFrame()

    # ==================== 向后兼容方法 ====================

    def create_table(self, table_name: str, df: pd.DataFrame, if_exists: str = "replace"):
        """
        向后兼容方法：创建表
        建议使用 create_table_from_df 方法
        """
        logger.warning("create_table method is deprecated, use create_table_from_df instead")
        return self.create_table_from_df(table_name, df, if_exists)

    def upsert_df(self, table_name, df: pd.DataFrame, key_cols):
        """
        基于主键列upsert数据（duckdb 0.9+支持merge语法）
        """
        # 先插入到临时表
        self.conn.register("tmp_df", df)
        merge_keys = " AND ".join([f"t.{k}=s.{k}" for k in key_cols])
        update_set = ", ".join([f"{col}=s.{col}" for col in df.columns if col not in key_cols])
        insert_cols = ", ".join(df.columns)
        insert_vals = ", ".join([f"s.{col}" for col in df.columns])
        sql = f"""
        MERGE INTO {table_name} t
        USING tmp_df s
        ON {merge_keys}
        WHEN MATCHED THEN UPDATE SET {update_set}
        WHEN NOT MATCHED THEN INSERT ({insert_cols}) VALUES ({insert_vals})
        """
        self.conn.execute(sql)
        self.conn.unregister("tmp_df")

    def close(self):
        self.conn.close()

