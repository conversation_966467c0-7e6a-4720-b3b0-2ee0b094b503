"""
因子可视化模块

创建因子分析结果的可视化展示，包括IC时序图、累计收益图、分层收益图等。
"""

from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

# 设置中文字体和样式 - 强化版本
def setup_chinese_fonts():
    """设置中文字体"""
    try:
        # 强制设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'DejaVu Sans', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.size'] = 10

        # 设置seaborn样式
        sns.set_style("whitegrid")

        return True
    except Exception as e:
        logger.warning(f"Failed to setup Chinese fonts: {e}")
        return False

# 初始化字体设置
CHINESE_FONT_AVAILABLE = setup_chinese_fonts()


class FactorVisualizer:
    """
    因子可视化器，负责生成各种分析图表
    
    主要功能:
    - IC时序图
    - 累计收益图
    - 分位数收益图
    - 因子分布图
    - 换手率分析图
    - 因子衰减图
    - 因子比较图
    """
    
    def __init__(self, figure_size: Tuple[int, int] = (12, 8), dpi: int = 300, style: str = "seaborn"):
        """
        初始化可视化器
        
        Parameters:
        -----------
        figure_size : Tuple[int, int], optional
            图表大小，默认(12, 8)
        dpi : int, optional
            图表分辨率，默认300
        style : str, optional
            图表样式，默认"seaborn"
        """
        self.figure_size = figure_size
        self.dpi = dpi
        self.style = style
        
        # 设置样式
        if style == "seaborn":
            sns.set_style("whitegrid")
        
    def plot_ic_analysis(self, ic_data: pd.DataFrame, save_path: str = None,
                        factor_name: str = "Factor") -> plt.Figure:
        """
        绘制IC分析图

        Parameters:
        -----------
        ic_data : pd.DataFrame
            IC时序数据
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称

        Returns:
        --------
        plt.Figure
            图表对象
        """
        try:
            # 强制设置中文字体
            setup_chinese_fonts()

            fig, axes = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle(f'{factor_name} - IC分析', fontsize=16, fontweight='bold')
            
            # 1. IC时序图
            ax1 = axes[0, 0]
            for period in ic_data.columns:
                ax1.plot(ic_data.index, ic_data[period], alpha=0.7)
            ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            ax1.set_title('IC时序图')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('IC值')

            # 设置中文图例
            if CHINESE_FONT_AVAILABLE:
                legend_labels = [f'{period}期' for period in ic_data.columns]
                ax1.legend(legend_labels, loc='best', fontsize=10)
            else:
                legend_labels = [f'{period}D' for period in ic_data.columns]
                ax1.legend(legend_labels, loc='best', fontsize=10)
            ax1.grid(True, alpha=0.3)
            
            # 2. IC分布直方图
            ax2 = axes[0, 1]
            ic_data.hist(bins=30, alpha=0.7, ax=ax2)
            ax2.set_title('IC分布直方图')
            ax2.set_xlabel('IC值')
            ax2.set_ylabel('频数')
            
            # 3. IC累计图
            ax3 = axes[1, 0]
            ic_cumsum = ic_data.cumsum()
            for period in ic_cumsum.columns:
                ax3.plot(ic_cumsum.index, ic_cumsum[period])
            ax3.set_title('IC累计图')
            ax3.set_xlabel('日期')
            ax3.set_ylabel('累计IC')

            # 设置中文图例
            if CHINESE_FONT_AVAILABLE:
                legend_labels = [f'{period}期' for period in ic_cumsum.columns]
                ax3.legend(legend_labels, loc='best', fontsize=10)
            else:
                legend_labels = [f'{period}D' for period in ic_cumsum.columns]
                ax3.legend(legend_labels, loc='best', fontsize=10)
            ax3.grid(True, alpha=0.3)
            
            # 4. IC统计表
            ax4 = axes[1, 1]
            ax4.axis('off')
            ic_stats = ic_data.describe()
            table_data = []
            for stat in ['mean', 'std', 'min', 'max']:
                row = [stat] + [f"{ic_stats.loc[stat, col]:.4f}" for col in ic_stats.columns]
                table_data.append(row)
            
            table = ax4.table(cellText=table_data,
                            colLabels=['统计量'] + [f'{col}期' for col in ic_stats.columns],
                            cellLoc='center',
                            loc='center')
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            ax4.set_title('IC统计表')
            
            plt.tight_layout()
            
            if save_path:
                self._save_figure(fig, save_path, 'ic_analysis')
                
            return fig
            
        except Exception as e:
            logger.error(f"Error plotting IC analysis: {str(e)}")
            raise
            
    def plot_quantile_returns(self, quantile_returns: pd.DataFrame, save_path: str = None,
                            factor_name: str = "Factor") -> plt.Figure:
        """
        绘制分位数收益图

        Parameters:
        -----------
        quantile_returns : pd.DataFrame
            分位数收益数据
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称

        Returns:
        --------
        plt.Figure
            图表对象
        """
        try:
            # 强制设置中文字体
            setup_chinese_fonts()

            fig, axes = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle(f'{factor_name} - 分位数收益分析', fontsize=16, fontweight='bold')
            
            # 1. 分位数平均收益柱状图
            ax1 = axes[0, 0]
            periods = quantile_returns.columns.get_level_values(0).unique()
            x = np.arange(len(quantile_returns.index))
            width = 0.8 / len(periods)
            
            for i, period in enumerate(periods):
                returns = quantile_returns[period]
                ax1.bar(x + i * width, returns, width, alpha=0.7)

            ax1.set_title('分位数平均收益')
            ax1.set_xlabel('分位数')
            ax1.set_ylabel('平均收益')
            ax1.set_xticks(x + width * (len(periods) - 1) / 2)
            # 修复分位数标签的中文显示
            if CHINESE_FONT_AVAILABLE:
                ax1.set_xticklabels([f'第{i}分位数' for i in quantile_returns.index])
                # 设置中文图例
                legend_labels = [f'{period}期' for period in periods]
                ax1.legend(legend_labels, loc='best', fontsize=10)
            else:
                ax1.set_xticklabels([f'Q{i}' for i in quantile_returns.index])
                legend_labels = [f'{period}D' for period in periods]
                ax1.legend(legend_labels, loc='best', fontsize=10)
            ax1.grid(True, alpha=0.3)
            
            # 2. 分位数收益热力图
            ax2 = axes[0, 1]
            sns.heatmap(quantile_returns.T, annot=True, fmt='.4f', cmap='RdYlBu_r', ax=ax2)
            ax2.set_title('分位数收益热力图')
            ax2.set_xlabel('分位数')
            ax2.set_ylabel('持有期')
            
            # 3. 多空收益图
            ax3 = axes[1, 0]
            if len(quantile_returns.index) >= 2:
                long_short = quantile_returns.iloc[-1] - quantile_returns.iloc[0]  # 最高分位数 - 最低分位数
                periods = long_short.index
                ax3.bar(range(len(periods)), long_short.values, alpha=0.7, color='green')
                ax3.set_title('多空收益 (Q5-Q1)')
                ax3.set_xlabel('持有期')
                ax3.set_ylabel('收益差')
                ax3.set_xticks(range(len(periods)))
                ax3.set_xticklabels([f'{p}期' for p in periods])
                ax3.grid(True, alpha=0.3)
            
            # 4. 分位数收益统计表
            ax4 = axes[1, 1]
            ax4.axis('off')
            stats_data = []
            for period in periods:
                period_returns = quantile_returns[period]
                stats_data.append([
                    f'{period}期',
                    f"{period_returns.mean():.4f}",
                    f"{period_returns.std():.4f}",
                    f"{period_returns.max():.4f}",
                    f"{period_returns.min():.4f}"
                ])
                
            table = ax4.table(cellText=stats_data,
                            colLabels=['持有期', '均值', '标准差', '最大值', '最小值'],
                            cellLoc='center',
                            loc='center')
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            ax4.set_title('收益统计表')
            
            plt.tight_layout()
            
            if save_path:
                self._save_figure(fig, save_path, 'quantile_returns')
                
            return fig
            
        except Exception as e:
            logger.error(f"Error plotting quantile returns: {str(e)}")
            raise
            
    def plot_cumulative_returns(self, cumulative_returns: pd.DataFrame, save_path: str = None,
                              factor_name: str = "Factor") -> plt.Figure:
        """
        绘制累计收益图

        Parameters:
        -----------
        cumulative_returns : pd.DataFrame
            累计收益数据
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称

        Returns:
        --------
        plt.Figure
            图表对象
        """
        try:
            # 强制设置中文字体
            setup_chinese_fonts()

            fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
            
            for period in cumulative_returns.columns:
                ax.plot(cumulative_returns.index, cumulative_returns[period], linewidth=2)

            ax.set_title(f'{factor_name} - 累计收益图', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('累计收益')

            # 设置中文图例
            if CHINESE_FONT_AVAILABLE:
                legend_labels = [f'{period}期' for period in cumulative_returns.columns]
                ax.legend(legend_labels, loc='best', fontsize=10)
            else:
                legend_labels = [f'{period}D' for period in cumulative_returns.columns]
                ax.legend(legend_labels, loc='best', fontsize=10)
            ax.grid(True, alpha=0.3)
            
            # 添加零线
            ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            
            plt.tight_layout()
            
            if save_path:
                self._save_figure(fig, save_path, 'cumulative_returns')
                
            return fig
            
        except Exception as e:
            logger.error(f"Error plotting cumulative returns: {str(e)}")
            raise
            
    def plot_factor_distribution(self, factor_data: pd.Series, save_path: str = None,
                               factor_name: str = "Factor") -> plt.Figure:
        """
        绘制因子分布图

        Parameters:
        -----------
        factor_data : pd.Series
            因子数据
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称

        Returns:
        --------
        plt.Figure
            图表对象
        """
        try:
            # 强制设置中文字体
            setup_chinese_fonts()

            fig, axes = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle(f'{factor_name} - 因子分布分析', fontsize=16, fontweight='bold')
            
            # 1. 直方图
            ax1 = axes[0, 0]
            factor_data.hist(bins=50, alpha=0.7, ax=ax1)
            ax1.set_title('因子分布直方图')
            ax1.set_xlabel('因子值')
            ax1.set_ylabel('频数')
            ax1.grid(True, alpha=0.3)
            
            # 2. 箱线图
            ax2 = axes[0, 1]
            factor_data.plot.box(ax=ax2)
            ax2.set_title('因子分布箱线图')
            ax2.set_ylabel('因子值')
            ax2.grid(True, alpha=0.3)
            
            # 3. Q-Q图
            ax3 = axes[1, 0]
            from scipy import stats
            stats.probplot(factor_data.dropna(), dist="norm", plot=ax3)
            ax3.set_title('Q-Q图 (正态性检验)')
            ax3.grid(True, alpha=0.3)
            
            # 4. 统计信息
            ax4 = axes[1, 1]
            ax4.axis('off')
            
            stats_info = factor_data.describe()
            stats_data = [
                ['样本数', f"{len(factor_data)}"],
                ['均值', f"{stats_info['mean']:.4f}"],
                ['标准差', f"{stats_info['std']:.4f}"],
                ['最小值', f"{stats_info['min']:.4f}"],
                ['25%分位数', f"{stats_info['25%']:.4f}"],
                ['中位数', f"{stats_info['50%']:.4f}"],
                ['75%分位数', f"{stats_info['75%']:.4f}"],
                ['最大值', f"{stats_info['max']:.4f}"]
            ]
            
            table = ax4.table(cellText=stats_data,
                            colLabels=['统计量', '数值'],
                            cellLoc='center',
                            loc='center')
            table.auto_set_font_size(False)
            table.set_fontsize(12)
            table.scale(1.2, 1.5)

            # 终极表格中文字体设置 - 使用最直接的方法
            try:
                import matplotlib.font_manager as fm

                # 尝试多种字体设置方法
                font_methods = [
                    ('SimHei', 'C:\\Windows\\Fonts\\simhei.ttf'),
                    ('Microsoft YaHei', 'C:\\Windows\\Fonts\\msyh.ttc'),
                    ('SimSun', 'C:\\Windows\\Fonts\\simsun.ttc')
                ]

                font_applied = False
                for _, font_path in font_methods:
                    try:
                        # 检查字体文件是否存在
                        import os
                        if os.path.exists(font_path):
                            # 方法1: 使用字体路径创建FontProperties
                            font_prop = fm.FontProperties(fname=font_path)
                            for cell in table.get_celld().values():
                                cell.set_text_props(fontproperties=font_prop)
                            font_applied = True
                            break
                    except Exception:
                        continue

                # 如果上面的方法都失败，使用备用方法
                if not font_applied:
                    try:
                        # 方法2: 直接使用字体名称
                        for cell in table.get_celld().values():
                            cell.set_text_props(fontname='SimHei')
                            cell.set_text_props(fontfamily=['SimHei', 'Microsoft YaHei', 'SimSun'])
                    except Exception:
                        pass

            except Exception as e:
                logger.warning(f"Failed to set table Chinese font: {e}")

            ax4.set_title('统计信息')
            
            plt.tight_layout()
            
            if save_path:
                self._save_figure(fig, save_path, 'factor_distribution')
                
            return fig
            
        except Exception as e:
            logger.error(f"Error plotting factor distribution: {str(e)}")
            raise

    def plot_turnover_analysis(self, turnover_data: pd.DataFrame, save_path: str = None,
                             factor_name: str = "Factor") -> plt.Figure:
        """
        绘制换手率分析图

        Parameters:
        -----------
        turnover_data : pd.DataFrame
            换手率数据
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称

        Returns:
        --------
        plt.Figure
            图表对象
        """
        try:
            # 强制设置中文字体
            setup_chinese_fonts()

            fig, axes = plt.subplots(1, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle(f'{factor_name} - 换手率分析', fontsize=16, fontweight='bold')

            # 1. 换手率时序图
            ax1 = axes[0]
            for period in turnover_data.columns:
                ax1.plot(turnover_data.index, turnover_data[period], alpha=0.7)
            ax1.set_title('换手率时序图')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('换手率')

            # 设置中文图例
            if CHINESE_FONT_AVAILABLE:
                legend_labels = [f'{period}期' for period in turnover_data.columns]
                ax1.legend(legend_labels, loc='best', fontsize=10)
            else:
                legend_labels = [f'{period}D' for period in turnover_data.columns]
                ax1.legend(legend_labels, loc='best', fontsize=10)
            ax1.grid(True, alpha=0.3)

            # 2. 平均换手率柱状图
            ax2 = axes[1]
            avg_turnover = turnover_data.mean()
            ax2.bar(range(len(avg_turnover)), avg_turnover.values, alpha=0.7)
            ax2.set_title('平均换手率')
            ax2.set_xlabel('持有期')
            ax2.set_ylabel('平均换手率')
            ax2.set_xticks(range(len(avg_turnover)))
            ax2.set_xticklabels([f'{p}期' for p in avg_turnover.index])
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()

            if save_path:
                self._save_figure(fig, save_path, 'turnover_analysis')

            return fig

        except Exception as e:
            logger.error(f"Error plotting turnover analysis: {str(e)}")
            raise

    def plot_factor_decay(self, decay_data: Dict[str, pd.DataFrame], save_path: str = None,
                         factor_name: str = "Factor") -> plt.Figure:
        """
        绘制因子衰减图

        Parameters:
        -----------
        decay_data : Dict[str, pd.DataFrame]
            衰减数据，包含'ic_decay'和'returns_decay'
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称

        Returns:
        --------
        plt.Figure
            图表对象
        """
        try:
            # 强制设置中文字体
            setup_chinese_fonts()

            fig, axes = plt.subplots(1, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle(f'{factor_name} - 因子衰减分析', fontsize=16, fontweight='bold')

            # 1. IC衰减图
            if 'ic_decay' in decay_data:
                ax1 = axes[0]
                ic_decay = decay_data['ic_decay']
                periods = ic_decay.columns
                ic_mean = ic_decay.mean()

                ax1.plot(periods, ic_mean.values, marker='o', linewidth=2)
                ax1.set_title('IC衰减图')
                ax1.set_xlabel('持有期')
                ax1.set_ylabel('平均IC')
                ax1.grid(True, alpha=0.3)
                ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)

            # 2. 收益衰减图
            if 'returns_decay' in decay_data:
                ax2 = axes[1]
                returns_decay = decay_data['returns_decay']
                periods = returns_decay.columns.get_level_values(0).unique()

                # 计算多空收益衰减
                long_short_decay = []
                for period in periods:
                    period_returns = returns_decay[period]
                    if len(period_returns) >= 2:
                        long_short = period_returns.iloc[-1] - period_returns.iloc[0]
                        long_short_decay.append(long_short)
                    else:
                        long_short_decay.append(0)

                ax2.plot(periods, long_short_decay, marker='o', linewidth=2, color='green')
                ax2.set_title('多空收益衰减图')
                ax2.set_xlabel('持有期')
                ax2.set_ylabel('多空收益')
                ax2.grid(True, alpha=0.3)
                ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)

            plt.tight_layout()

            if save_path:
                self._save_figure(fig, save_path, 'factor_decay')

            return fig

        except Exception as e:
            logger.error(f"Error plotting factor decay: {str(e)}")
            raise

    def plot_factor_comparison(self, comparison_data: Dict[str, Any], save_path: str = None) -> plt.Figure:
        """
        绘制因子比较图

        Parameters:
        -----------
        comparison_data : Dict[str, Any]
            因子比较数据
        save_path : str, optional
            保存路径

        Returns:
        --------
        plt.Figure
            图表对象
        """
        try:
            # 强制设置中文字体
            setup_chinese_fonts()

            fig, axes = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle('因子表现比较', fontsize=16, fontweight='bold')

            # 1. IC均值比较
            if 'ic_mean' in comparison_data:
                ax1 = axes[0, 0]
                ic_mean_data = comparison_data['ic_mean']
                factors = list(ic_mean_data.keys())
                ic_values = list(ic_mean_data.values())

                bars = ax1.bar(factors, ic_values, alpha=0.7)
                ax1.set_title('IC均值比较')
                ax1.set_ylabel('IC均值')
                ax1.tick_params(axis='x', rotation=45)
                ax1.grid(True, alpha=0.3)

                # 添加数值标签
                for bar, value in zip(bars, ic_values):
                    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                            f'{value:.4f}', ha='center', va='bottom')

            # 2. IC信息比率比较
            if 'ic_ir' in comparison_data:
                ax2 = axes[0, 1]
                ic_ir_data = comparison_data['ic_ir']
                factors = list(ic_ir_data.keys())
                ir_values = list(ic_ir_data.values())

                bars = ax2.bar(factors, ir_values, alpha=0.7, color='orange')
                ax2.set_title('IC信息比率比较')
                ax2.set_ylabel('信息比率')
                ax2.tick_params(axis='x', rotation=45)
                ax2.grid(True, alpha=0.3)

                # 添加数值标签
                for bar, value in zip(bars, ir_values):
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                            f'{value:.4f}', ha='center', va='bottom')

            # 3. 多空收益比较
            if 'spread_mean' in comparison_data:
                ax3 = axes[1, 0]
                spread_data = comparison_data['spread_mean']
                factors = list(spread_data.keys())
                spread_values = list(spread_data.values())

                bars = ax3.bar(factors, spread_values, alpha=0.7, color='green')
                ax3.set_title('多空收益比较')
                ax3.set_ylabel('多空收益')
                ax3.tick_params(axis='x', rotation=45)
                ax3.grid(True, alpha=0.3)

                # 添加数值标签
                for bar, value in zip(bars, spread_values):
                    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.0001,
                            f'{value:.4f}', ha='center', va='bottom')

            # 4. 综合评分雷达图
            ax4 = axes[1, 1]
            if all(key in comparison_data for key in ['ic_mean', 'ic_ir', 'spread_mean']):
                factors = list(comparison_data['ic_mean'].keys())

                # 标准化各项指标到0-1范围
                metrics = ['IC均值', 'IC信息比率', '多空收益']

                angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
                angles += angles[:1]  # 闭合图形

                ax4 = plt.subplot(2, 2, 4, projection='polar')

                for factor in factors[:3]:  # 最多显示3个因子
                    values = []
                    if factor in comparison_data['ic_mean']:
                        ic_val = comparison_data['ic_mean'][factor]
                        values.append(abs(ic_val) * 10)  # 放大显示
                    if factor in comparison_data['ic_ir']:
                        ir_val = comparison_data['ic_ir'][factor]
                        values.append(abs(ir_val))
                    if factor in comparison_data['spread_mean']:
                        spread_val = comparison_data['spread_mean'][factor]
                        values.append(abs(spread_val) * 1000)  # 放大显示

                    values += values[:1]  # 闭合图形

                    ax4.plot(angles, values, 'o-', linewidth=2)
                    ax4.fill(angles, values, alpha=0.25)

                ax4.set_xticks(angles[:-1])
                ax4.set_xticklabels(metrics)
                ax4.set_title('综合表现雷达图')

                # 设置中文图例
                if CHINESE_FONT_AVAILABLE:
                    ax4.legend(list(comparison_data['ic_mean'].keys()), loc='best', fontsize=10)
                else:
                    ax4.legend(list(comparison_data['ic_mean'].keys()), loc='best', fontsize=10)

            plt.tight_layout()

            if save_path:
                self._save_figure(fig, save_path, 'factor_comparison')

            return fig

        except Exception as e:
            logger.error(f"Error plotting factor comparison: {str(e)}")
            raise

    def _save_figure(self, fig: plt.Figure, save_path: str, chart_type: str):
        """
        保存图表

        Parameters:
        -----------
        fig : plt.Figure
            图表对象
        save_path : str
            保存路径
        chart_type : str
            图表类型
        """
        try:
            # 确保目录存在
            Path(save_path).mkdir(parents=True, exist_ok=True)

            # 生成文件名
            filename = f"{chart_type}.png"
            full_path = os.path.join(save_path, filename)

            # 保存图表
            fig.savefig(full_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')

            logger.info(f"Chart saved to: {full_path}")

        except Exception as e:
            logger.error(f"Error saving figure: {str(e)}")

    def create_analysis_report(self, analysis_results: Dict[str, Any],
                             save_path: str = None, factor_name: str = "Factor") -> List[plt.Figure]:
        """
        创建完整的分析报告图表

        Parameters:
        -----------
        analysis_results : Dict[str, Any]
            分析结果
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称

        Returns:
        --------
        List[plt.Figure]
            图表列表
        """
        figures = []

        try:
            # 1. IC分析图
            if 'ic_analysis' in analysis_results and 'ic_time_series' in analysis_results['ic_analysis']:
                ic_fig = self.plot_ic_analysis(
                    analysis_results['ic_analysis']['ic_time_series'],
                    save_path, factor_name
                )
                figures.append(ic_fig)

            # 2. 分位数收益图
            if 'quantile_returns' in analysis_results and 'mean_returns_by_quantile' in analysis_results['quantile_returns']:
                quantile_fig = self.plot_quantile_returns(
                    analysis_results['quantile_returns']['mean_returns_by_quantile'],
                    save_path, factor_name
                )
                figures.append(quantile_fig)

            # 3. 累计收益图
            if 'factor_returns' in analysis_results and 'cumulative_returns' in analysis_results['factor_returns']:
                cumulative_fig = self.plot_cumulative_returns(
                    analysis_results['factor_returns']['cumulative_returns'],
                    save_path, factor_name
                )
                figures.append(cumulative_fig)

            # 4. 换手率分析图
            if 'turnover_analysis' in analysis_results and 'quantile_turnover' in analysis_results['turnover_analysis']:
                turnover_fig = self.plot_turnover_analysis(
                    analysis_results['turnover_analysis']['quantile_turnover'],
                    save_path, factor_name
                )
                figures.append(turnover_fig)

            logger.info(f"Created {len(figures)} analysis charts for factor {factor_name}")
            return figures

        except Exception as e:
            logger.error(f"Error creating analysis report: {str(e)}")
            return figures
