# 因子研究框架（统一版）

基于alphalens的单因子研究框架，采用统一架构设计，提供一站式的因子计算、分析和可视化功能。

## 主要特性

- **统一接口**: 使用FactorResearch提供一站式因子研究解决方案
- **因子计算**: 支持多种技术指标和量价因子
- **因子分析**: 基于alphalens的专业因子分析，支持可选依赖模式
- **批量处理**: 支持多因子并行计算和批量分析
- **可视化**: 丰富的图表和报告生成
- **扩展性**: 易于添加新的因子类型
- **配置化**: 灵活的配置管理系统
- **高性能**: 内置缓存和并行计算支持

## 快速开始

```python
from factor_research import FactorResearch

# 初始化因子研究框架（统一接口）
factor_research = FactorResearch(db_manage=your_db_manage)

# 计算单个因子
factor_data = factor_research.calculate_factor(
    factor_name='MomentumFactor',
    assets=['000001.SZ', '000002.SZ'],
    start_date='2023-01-01',
    end_date='2023-12-31'
)

# 分析因子
results = factor_research.analyze_factor(factor_data)

# 批量计算多个因子
factor_data_dict = factor_research.calculate_multiple_factors(
    factor_names=['MomentumFactor', 'RSIFactor'],
    assets=['000001.SZ', '000002.SZ'],
    start_date='2023-01-01',
    end_date='2023-12-31'
)

# 比较多个因子
comparison_results = factor_research.compare_factors(factor_data_dict)

# 可视化结果
from factor_research import FactorVisualizer
visualizer = FactorVisualizer()
visualizer.plot_factor_analysis(results)
```

## 安装依赖

### 必需依赖
```bash
pip install pandas numpy matplotlib seaborn PyYAML
```

### 可选依赖（推荐）
```bash
pip install alphalens  # 提供完整的因子分析功能
```

## 架构优化

### v2.1.0 更新内容

- **统一接口**: 新增`FactorResearch`类，整合了因子计算、分析和可视化功能
- **简化架构**: 移除了冗余的适配器层，直接使用data_engine
- **性能优化**: 内置缓存机制和并行计算支持
- **向后兼容**: 保留原有接口，支持渐进式迁移

### 目录结构

```
factor_research/
├── core/                   # 核心模块（统一版）
│   ├── factor_research.py  # 统一因子研究框架（推荐）
│   ├── factor_engine.py    # 因子计算引擎（兼容）
│   ├── factor_analyzer.py  # 因子分析器（兼容）
│   ├── data_adapter.py     # 数据适配器（兼容）
│   └── visualizer.py       # 可视化组件
├── factors/                # 因子库
│   ├── base.py            # 基础因子类
│   └── momentum.py        # 动量类因子
├── utils/                 # 工具模块
│   ├── config.py          # 配置管理
│   └── exceptions.py      # 异常定义
├── doc/                   # 文档目录
│   ├── README.md          # 文档索引
│   ├── USER_GUIDE.md      # 用户指南
│   ├── API_DESIGN.md      # API设计
│   ├── FACTOR_LIBRARY.md  # 因子库文档
│   ├── QUICK_START.md     # 快速开始
│   └── ...               # 其他文档
└── examples/              # 使用示例
```

## 📚 文档

完整文档请参考 **[doc/](doc/)** 目录：

- **[文档索引](doc/README.md)** - 所有文档的导航
- **[快速开始](doc/QUICK_START.md)** - 5分钟快速体验
- **[用户指南](doc/USER_GUIDE.md)** - 详细使用说明
- **[API设计](doc/API_DESIGN.md)** - 接口文档
- **[因子库文档](doc/FACTOR_LIBRARY.md)** - 因子扩展指南
- **[实现总结](doc/IMPLEMENTATION_SUMMARY.md)** - 项目完整总结

## 🧪 测试

运行测试验证框架功能：

```bash
cd examples
python simple_test.py
```

## 📄 许可证

MIT License
