#!/usr/bin/env python3
"""
环境检查脚本

检查Python环境和依赖包是否正确安装，诊断常见问题。
"""

import sys
import importlib
import platform
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3:
        print("❌ 错误: 需要Python 3.x版本")
        return False
    elif version.minor < 8:
        print("⚠️  警告: 建议使用Python 3.8+版本以获得最佳兼容性")
    else:
        print("✅ Python版本符合要求")
    
    return True

def check_package(package_name, import_name=None, required=True):
    """检查单个包是否安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        status = "✅" if required else "🟢"
        print(f"{status} {package_name}: {version}")
        return True
    except ImportError:
        status = "❌" if required else "⚠️ "
        level = "错误" if required else "可选"
        print(f"{status} {package_name}: 未安装 ({level})")
        return False

def check_core_dependencies():
    """检查核心依赖"""
    print("\n📦 检查核心依赖...")
    
    core_packages = [
        ("PyQt5", "PyQt5.QtWidgets"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("duckdb", "duckdb"),
        ("akshare", "akshare"),
        ("talib", "talib"),
        ("matplotlib", "matplotlib"),
        ("seaborn", "seaborn"),
        ("bokeh", "bokeh"),
        ("PyYAML", "yaml"),
        ("scipy", "scipy")
    ]
    
    success_count = 0
    for package_name, import_name in core_packages:
        if check_package(package_name, import_name, required=True):
            success_count += 1
    
    print(f"\n📊 核心依赖检查结果: {success_count}/{len(core_packages)} 已安装")
    return success_count == len(core_packages)

def check_optional_dependencies():
    """检查可选依赖"""
    print("\n🎯 检查可选依赖...")
    
    optional_packages = [
        ("alphalens", "alphalens"),
        ("backtrader", "backtrader"),
        ("scikit-learn", "sklearn"),
        ("pytest", "pytest")
    ]
    
    for package_name, import_name in optional_packages:
        check_package(package_name, import_name, required=False)

def check_system_info():
    """检查系统信息"""
    print("\n💻 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   架构: {platform.machine()}")
    print(f"   Python路径: {sys.executable}")

def check_project_structure():
    """检查项目结构"""
    print("\n📁 检查项目结构...")
    
    required_dirs = [
        "gui", "database", "engine", "factor_research", 
        "portfolio", "common", "config"
    ]
    
    required_files = [
        "main.py", "requirements.txt", "requirements-core.txt"
    ]
    
    project_root = Path(".")
    
    # 检查目录
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"✅ 目录: {dir_name}")
        else:
            print(f"❌ 目录缺失: {dir_name}")
    
    # 检查文件
    for file_name in required_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"✅ 文件: {file_name}")
        else:
            print(f"❌ 文件缺失: {file_name}")

def check_database_files():
    """检查数据库文件"""
    print("\n🗄️  检查数据库文件...")
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return False
    
    db_files = [
        "quant_data.duckdb",
        "account_data.duckdb", 
        "snapshots.duckdb"
    ]
    
    for db_file in db_files:
        db_path = data_dir / db_file
        if db_path.exists():
            size = db_path.stat().st_size
            print(f"✅ {db_file}: {size/1024:.1f}KB")
        else:
            print(f"⚠️  {db_file}: 不存在（首次运行时会自动创建）")

def run_quick_test():
    """运行快速测试"""
    print("\n🧪 运行快速测试...")
    
    try:
        # 测试GUI导入
        from PyQt5.QtWidgets import QApplication
        print("✅ GUI框架导入成功")
        
        # 测试数据处理
        import pandas as pd
        import numpy as np
        df = pd.DataFrame({'test': [1, 2, 3]})
        print("✅ 数据处理库测试成功")
        
        # 测试数据库
        import duckdb
        conn = duckdb.connect(":memory:")
        conn.execute("SELECT 1")
        print("✅ 数据库连接测试成功")
        
        # 测试因子研究框架
        try:
            from factor_research import FactorResearch
            print("✅ 因子研究框架导入成功")
        except ImportError as e:
            print(f"⚠️  因子研究框架导入失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n💡 常见问题解决方案:")
    print("1. 如果缺少依赖包:")
    print("   pip install -r requirements-core.txt")
    print("   或运行: python install_dependencies.py")
    
    print("\n2. 如果talib安装失败 (Windows):")
    print("   下载预编译包: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
    print("   pip install TA_Lib-0.4.xx-cpxx-cpxxm-win_amd64.whl")
    
    print("\n3. 如果PyQt5安装失败:")
    print("   pip install PyQt5 --no-cache-dir")
    print("   或尝试: conda install pyqt")
    
    print("\n4. 如果alphalens安装失败:")
    print("   这是可选依赖，可以跳过")
    print("   或尝试: pip install alphalens --no-deps")

def main():
    """主函数"""
    print("🔍 T_TRADE 环境检查工具")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查系统信息
    check_system_info()
    
    # 检查项目结构
    check_project_structure()
    
    # 检查依赖
    core_ok = check_core_dependencies()
    check_optional_dependencies()
    
    # 检查数据库
    check_database_files()
    
    # 运行测试
    test_ok = run_quick_test()
    
    # 总结
    print("\n" + "="*50)
    print("📋 检查总结:")
    
    if core_ok and test_ok:
        print("🎉 环境检查通过！可以正常运行项目")
        print("启动命令: python main.py")
    else:
        print("⚠️  环境存在问题，请参考解决方案")
        provide_solutions()

if __name__ == "__main__":
    main()
