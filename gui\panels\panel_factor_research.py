"""
因子研究面板

提供图形界面进行因子研究，包括：
- 因子选择和参数配置
- 股票池选择
- 时间范围设置
- 因子计算和分析
- 结果可视化
- 报告生成
"""

import sys
import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QTabWidget,
    QPushButton, QLabel, QLineEdit, QComboBox, QListWidget, QTextEdit,
    QDateEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox,
    QProgressBar, QMessageBox, QFileDialog, QTableWidget, QTableWidgetItem,
    QSplitter, QFrame, QScrollArea
)
from PyQt5.QtCore import QDate, QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPixmap
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import List

# 导入matplotlib用于图表显示
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    from matplotlib import rcParams
    import matplotlib.font_manager as fm

    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    MATPLOTLIB_AVAILABLE = False
    logging.warning(f"Matplotlib not available: {e}")


def configure_chinese_fonts():
    """配置matplotlib中文字体 - 强制版本"""
    if not MATPLOTLIB_AVAILABLE:
        return False

    try:
        # 强制清除matplotlib字体缓存
        try:
            import matplotlib
            matplotlib.font_manager._rebuild()
        except:
            pass

        # 获取系统可用字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]

        # 中文字体优先级列表
        chinese_fonts = [
            'SimHei',           # 黑体
            'Microsoft YaHei',  # 微软雅黑
            'SimSun',           # 宋体
            'KaiTi',            # 楷体
            'FangSong',         # 仿宋
            'STHeiti',          # 华文黑体 (Mac)
            'STSong',           # 华文宋体 (Mac)
            'PingFang SC',      # 苹方 (Mac)
            'Hiragino Sans GB', # 冬青黑体 (Mac)
            'WenQuanYi Micro Hei', # 文泉驿微米黑 (Linux)
            'WenQuanYi Zen Hei',   # 文泉驿正黑 (Linux)
            'Noto Sans CJK SC',    # 思源黑体 (Linux)
            'Arial Unicode MS',    # 通用Unicode字体
            'DejaVu Sans'          # 备用字体
        ]

        # 查找可用的中文字体
        selected_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                selected_font = font
                break

        if selected_font:
            # 强制配置matplotlib字体
            plt.rcParams['font.sans-serif'] = [selected_font] + chinese_fonts
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.size'] = 10

            # 同时设置rcParams
            rcParams['font.sans-serif'] = [selected_font] + chinese_fonts
            rcParams['axes.unicode_minus'] = False
            rcParams['font.size'] = 10

            # 强制更新所有matplotlib配置
            plt.rcdefaults()
            plt.rcParams.update({
                'font.sans-serif': [selected_font] + chinese_fonts,
                'axes.unicode_minus': False,
                'font.size': 10
            })

            logging.info(f"Chinese font configured: {selected_font}")
            return True
        else:
            # 如果没有找到中文字体，使用默认配置
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False

            logging.warning("No Chinese fonts found, using default fonts")
            return False

    except Exception as e:
        logging.error(f"Failed to configure Chinese fonts: {e}")
        # 使用基本配置
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        return False


def get_chinese_font_props():
    """获取中文字体属性 - 强制版本"""
    if CHINESE_FONT_AVAILABLE and MATPLOTLIB_AVAILABLE:
        try:
            from matplotlib.font_manager import FontProperties
            # 直接使用第一个可用的中文字体
            font_name = plt.rcParams['font.sans-serif'][0]
            return FontProperties(family=font_name, size=10)
        except:
            return None
    return None


def force_chinese_font_setup():
    """强制设置中文字体 - 在每次绘图前调用"""
    if MATPLOTLIB_AVAILABLE:
        try:
            # 强制设置字体 - 使用最直接的方法
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'DejaVu Sans', 'Arial']
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.size'] = 10

            # 同时设置rcParams
            rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'DejaVu Sans', 'Arial']
            rcParams['axes.unicode_minus'] = False
            rcParams['font.size'] = 10

            return True
        except:
            return False
    return False


def apply_chinese_font_to_text(text_obj, font_name='SimHei'):
    """直接为文本对象应用中文字体"""
    if MATPLOTLIB_AVAILABLE:
        try:
            from matplotlib.font_manager import FontProperties
            font_prop = FontProperties(family=font_name, size=text_obj.get_fontsize())
            text_obj.set_fontproperties(font_prop)
            return True
        except:
            return False
    return False


# 初始化中文字体配置
if MATPLOTLIB_AVAILABLE:
    CHINESE_FONT_AVAILABLE = configure_chinese_fonts()
else:
    CHINESE_FONT_AVAILABLE = False

# 导入因子研究框架（简化版）
try:
    from factor_research import FactorEngine, FactorAnalyzer, DataAdapter, FactorVisualizer
    from factor_research.factors.momentum import MomentumFactor, RSIFactor, PriceVolumeTrendFactor
    FACTOR_RESEARCH_AVAILABLE = True
except ImportError as e:
    FACTOR_RESEARCH_AVAILABLE = False
    logging.warning(f"Factor research framework not available: {e}")

logger = logging.getLogger(__name__)


class FactorChartWidget(QWidget):
    """因子图表显示组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.figure = None
        self.canvas = None
        self.current_data = None
        self.current_analysis = None

        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        if not MATPLOTLIB_AVAILABLE:
            # 如果matplotlib不可用，显示提示信息
            error_label = QLabel("📊 图表功能需要安装matplotlib\n\n安装命令: pip install matplotlib")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("""
                QLabel {
                    color: #666666;
                    font-size: 14px;
                    background-color: #f8f8f8;
                    border: 2px dashed #cccccc;
                    border-radius: 8px;
                    padding: 20px;
                }
            """)
            layout.addWidget(error_label)
            self.setLayout(layout)
            return

        # 创建图表控制按钮
        button_layout = QHBoxLayout()

        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "因子时序图", "因子分布图", "IC分析图", "因子热力图", "收益分析图"
        ])
        self.chart_type_combo.currentTextChanged.connect(self._update_chart)

        refresh_btn = QPushButton("🔄 刷新图表")
        refresh_btn.clicked.connect(self._update_chart)
        refresh_btn.setMaximumWidth(100)

        export_btn = QPushButton("💾 导出图片")
        export_btn.clicked.connect(self._export_chart)
        export_btn.setMaximumWidth(100)

        button_layout.addWidget(QLabel("图表类型:"))
        button_layout.addWidget(self.chart_type_combo)
        button_layout.addStretch()
        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(export_btn)

        layout.addLayout(button_layout)

        # 创建matplotlib图表
        self.figure = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setMinimumHeight(400)

        # 添加滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.canvas)
        scroll_area.setWidgetResizable(True)

        layout.addWidget(scroll_area)

        # 初始显示
        self._show_placeholder()

        self.setLayout(layout)

    def _show_placeholder(self):
        """显示占位符"""
        if not MATPLOTLIB_AVAILABLE or not self.figure:
            return

        self.figure.clear()
        ax = self.figure.add_subplot(111)

        if CHINESE_FONT_AVAILABLE:
            placeholder_text = '📈 因子分析图表\n\n请先计算因子数据'
        else:
            placeholder_text = '📈 Factor Analysis Charts\n\nPlease calculate factor data first'

        # 设置占位符文本的字体
        if CHINESE_FONT_AVAILABLE:
            font_props = get_chinese_font_props()
            if font_props:
                ax.text(0.5, 0.5, placeholder_text,
                        ha='center', va='center', fontsize=16, color='gray',
                        transform=ax.transAxes, fontproperties=font_props)
            else:
                ax.text(0.5, 0.5, placeholder_text,
                        ha='center', va='center', fontsize=16, color='gray',
                        transform=ax.transAxes, fontfamily=['SimHei', 'Microsoft YaHei', 'SimSun'])
        else:
            ax.text(0.5, 0.5, placeholder_text,
                    ha='center', va='center', fontsize=16, color='gray',
                    transform=ax.transAxes)
        ax.set_xticks([])
        ax.set_yticks([])
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)

        self.canvas.draw()

    def update_data(self, factor_data, analysis_results=None):
        """更新图表数据"""
        self.current_data = factor_data
        self.current_analysis = analysis_results
        self._update_chart()

    def _update_chart(self):
        """更新图表显示"""
        if not MATPLOTLIB_AVAILABLE or not self.figure or self.current_data is None:
            return

        chart_type = self.chart_type_combo.currentText()

        try:
            self.figure.clear()

            if chart_type == "因子时序图":
                self._plot_factor_timeseries()
            elif chart_type == "因子分布图":
                self._plot_factor_distribution()
            elif chart_type == "IC分析图":
                self._plot_ic_analysis()
            elif chart_type == "因子热力图":
                self._plot_factor_heatmap()
            elif chart_type == "收益分析图":
                self._plot_return_analysis()

            self.figure.tight_layout()
            self.canvas.draw()

        except Exception as e:
            logger.error(f"Error updating chart: {e}")
            self._show_error_chart(str(e))

    def _plot_factor_timeseries(self):
        """绘制因子时序图"""
        if self.current_data is None:
            return

        # 强制设置中文字体
        force_chinese_font_setup()

        # 计算每日因子均值
        daily_factor = self.current_data.groupby(level=0).mean()

        ax = self.figure.add_subplot(111)

        # 转换索引为日期
        dates = pd.to_datetime(daily_factor.index)
        values = daily_factor.values

        # 绘制因子时序线
        line1 = ax.plot(dates, values, linewidth=2, color='#2196F3')[0]
        ax.fill_between(dates, values, alpha=0.3, color='#2196F3')

        # 添加移动平均线
        line2 = None
        if len(values) > 5:
            ma5 = pd.Series(values).rolling(5).mean()
            line2 = ax.plot(dates, ma5, '--', color='#FF9800')[0]

        # 直接设置中文标题和标签
        if CHINESE_FONT_AVAILABLE:
            ax.set_title('因子时序变化', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('因子值', fontsize=12)

            # 直接设置中文图例
            legend_labels = ['因子均值']
            if line2 is not None:
                legend_labels.append('5日均线')
            ax.legend(legend_labels, loc='best', fontsize=10)
        else:
            ax.set_title('Factor Time Series', fontsize=14, fontweight='bold')
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel('Factor Value', fontsize=12)

            legend_labels = ['Factor Mean']
            if line2 is not None:
                legend_labels.append('5-Day MA')
            ax.legend(legend_labels, loc='best', fontsize=10)

        ax.grid(True, alpha=0.3)

        # 格式化x轴日期
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        self.figure.autofmt_xdate()

    def _plot_factor_distribution(self):
        """绘制因子分布图"""
        if self.current_data is None:
            return

        values = self.current_data.values

        # 创建子图
        ax1 = self.figure.add_subplot(221)  # 直方图
        ax2 = self.figure.add_subplot(222)  # 箱线图
        ax3 = self.figure.add_subplot(223)  # Q-Q图
        ax4 = self.figure.add_subplot(224)  # 统计信息

        # 直方图
        ax1.hist(values, bins=50, alpha=0.7, color='#4CAF50', edgecolor='black')
        font_props = get_chinese_font_props()
        if CHINESE_FONT_AVAILABLE and font_props:
            ax1.set_title('因子分布直方图', fontsize=12, fontproperties=font_props)
            ax1.set_xlabel('因子值', fontsize=10, fontproperties=font_props)
            ax1.set_ylabel('频数', fontsize=10, fontproperties=font_props)
        else:
            ax1.set_title('Factor Distribution Histogram', fontsize=12)
            ax1.set_xlabel('Factor Value', fontsize=10)
            ax1.set_ylabel('Frequency', fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 箱线图
        ax2.boxplot(values, vert=True)
        if CHINESE_FONT_AVAILABLE and font_props:
            ax2.set_title('因子分布箱线图', fontsize=12, fontproperties=font_props)
            ax2.set_ylabel('因子值', fontsize=10, fontproperties=font_props)
        else:
            ax2.set_title('Factor Distribution Boxplot', fontsize=12)
            ax2.set_ylabel('Factor Value', fontsize=10)
        ax2.grid(True, alpha=0.3)

        # Q-Q图 (简化版)
        try:
            from scipy import stats
            stats.probplot(values, dist="norm", plot=ax3)
            if CHINESE_FONT_AVAILABLE and font_props:
                ax3.set_title('Q-Q图 (正态性检验)', fontsize=12, fontproperties=font_props)
            else:
                ax3.set_title('Q-Q Plot (Normality Test)', fontsize=12)
        except ImportError:
            # 如果scipy不可用，显示简化的散点图
            sorted_values = np.sort(values)
            theoretical_quantiles = np.linspace(0, 1, len(sorted_values))
            ax3.scatter(theoretical_quantiles, sorted_values, alpha=0.6)
            if CHINESE_FONT_AVAILABLE and font_props:
                ax3.set_title('数据分布散点图', fontsize=12, fontproperties=font_props)
                ax3.set_xlabel('理论分位数', fontsize=10, fontproperties=font_props)
                ax3.set_ylabel('实际值', fontsize=10, fontproperties=font_props)
            else:
                ax3.set_title('Data Distribution Scatter', fontsize=12)
                ax3.set_xlabel('Theoretical Quantiles', fontsize=10)
                ax3.set_ylabel('Actual Values', fontsize=10)
        ax3.grid(True, alpha=0.3)

        # 统计信息
        ax4.axis('off')

        # 计算统计指标
        try:
            from scipy import stats
            skewness = stats.skew(values)
            kurtosis_val = stats.kurtosis(values)
        except ImportError:
            # 简化的偏度和峰度计算
            mean_val = np.mean(values)
            std_val = np.std(values)
            skewness = np.mean(((values - mean_val) / std_val) ** 3) if std_val > 0 else 0
            kurtosis_val = np.mean(((values - mean_val) / std_val) ** 4) - 3 if std_val > 0 else 0

        # 根据字体可用性选择显示语言
        if CHINESE_FONT_AVAILABLE:
            stats_text = f"""
        统计信息:

        样本数量: {len(values):,}
        均值: {np.mean(values):.4f}
        标准差: {np.std(values):.4f}
        偏度: {skewness:.4f}
        峰度: {kurtosis_val:.4f}

        分位数:
        25%: {np.percentile(values, 25):.4f}
        50%: {np.percentile(values, 50):.4f}
        75%: {np.percentile(values, 75):.4f}
        """
        else:
            stats_text = f"""
        Statistics:

        Sample Size: {len(values):,}
        Mean: {np.mean(values):.4f}
        Std Dev: {np.std(values):.4f}
        Skewness: {skewness:.4f}
        Kurtosis: {kurtosis_val:.4f}

        Quantiles:
        25%: {np.percentile(values, 25):.4f}
        50%: {np.percentile(values, 50):.4f}
        75%: {np.percentile(values, 75):.4f}
        """
        # 设置统计信息文本的字体
        if CHINESE_FONT_AVAILABLE:
            font_props = get_chinese_font_props()
            if font_props:
                ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes,
                        fontsize=10, verticalalignment='top', fontproperties=font_props)
            else:
                ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes,
                        fontsize=10, verticalalignment='top', fontfamily=['SimHei', 'Microsoft YaHei', 'SimSun'])
        else:
            ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes,
                    fontsize=10, verticalalignment='top', fontfamily='monospace')

    def _plot_ic_analysis(self):
        """绘制IC分析图"""
        # 检查IC数据是否可用
        ic_data = None

        # 尝试从不同的结构中获取IC数据
        if self.current_analysis is not None:
            # 新的结构：直接在analysis_results中
            if 'ic_data' in self.current_analysis:
                ic_data = self.current_analysis['ic_data']
            # 旧的结构：在ic_analysis子字典中
            elif ('ic_analysis' in self.current_analysis and
                  self.current_analysis['ic_analysis'] and
                  'ic_time_series' in self.current_analysis['ic_analysis']):
                ic_data = self.current_analysis['ic_analysis']['ic_time_series']

        # 如果没有IC数据，显示错误信息
        # Check if ic_data is None or empty using proper DataFrame/Series methods
        is_empty = False
        if ic_data is not None and hasattr(ic_data, 'empty'):
            is_empty = ic_data.empty
        if ic_data is None or is_empty:
            ax = self.figure.add_subplot(111)
            if CHINESE_FONT_AVAILABLE:
                error_text = 'IC分析数据不可用\n需要完整的因子分析结果'
            else:
                error_text = 'IC Analysis Data Unavailable\nComplete factor analysis results required'
            # 设置错误文本的字体
            if CHINESE_FONT_AVAILABLE:
                font_props = get_chinese_font_props()
                if font_props:
                    ax.text(0.5, 0.5, error_text,
                            ha='center', va='center', fontsize=14, color='gray',
                            transform=ax.transAxes, fontproperties=font_props)
                else:
                    ax.text(0.5, 0.5, error_text,
                            ha='center', va='center', fontsize=14, color='gray',
                            transform=ax.transAxes, fontfamily=['SimHei', 'Microsoft YaHei', 'SimSun'])
            else:
                ax.text(0.5, 0.5, error_text,
                        ha='center', va='center', fontsize=14, color='gray',
                        transform=ax.transAxes)
            return

        # 使用IC数据
        ic_series = ic_data

        # 创建子图
        ax1 = self.figure.add_subplot(211)  # IC时序图
        ax2 = self.figure.add_subplot(212)  # IC累积图

        # IC时序图
        # 如果ic_series是DataFrame，取第一列
        if isinstance(ic_series, pd.DataFrame):
            ic_series = ic_series.iloc[:, 0]

        dates = pd.to_datetime(ic_series.index)
        ic_values = ic_series.values

        ax1.plot(dates, ic_values, linewidth=1.5, color='#2196F3', alpha=0.7)
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax1.fill_between(dates, ic_values, 0, alpha=0.3, color='#2196F3')

        font_props = get_chinese_font_props()
        if CHINESE_FONT_AVAILABLE and font_props:
            ax1.set_title('IC时序图', fontsize=12, fontweight='bold', fontproperties=font_props)
            ax1.set_ylabel('IC值', fontsize=10, fontproperties=font_props)
        else:
            ax1.set_title('IC Time Series', fontsize=12, fontweight='bold')
            ax1.set_ylabel('IC Value', fontsize=10)
        ax1.grid(True, alpha=0.3)

        # IC累积图
        cumulative_ic = np.cumsum(ic_values)
        ax2.plot(dates, cumulative_ic, linewidth=2, color='#FF9800')
        ax2.fill_between(dates, cumulative_ic, alpha=0.3, color='#FF9800')

        if CHINESE_FONT_AVAILABLE and font_props:
            ax2.set_title('累积IC图', fontsize=12, fontweight='bold', fontproperties=font_props)
            ax2.set_xlabel('日期', fontsize=10, fontproperties=font_props)
            ax2.set_ylabel('累积IC', fontsize=10, fontproperties=font_props)
        else:
            ax2.set_title('Cumulative IC', fontsize=12, fontweight='bold')
            ax2.set_xlabel('Date', fontsize=10)
            ax2.set_ylabel('Cumulative IC', fontsize=10)
        ax2.grid(True, alpha=0.3)

        # 格式化日期
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))

        self.figure.autofmt_xdate()

    def _plot_factor_heatmap(self):
        """绘制因子热力图"""
        if self.current_data is None:
            return

        # 重塑数据为透视表格式
        df = self.current_data.reset_index()
        df.columns = ['date', 'asset', 'factor_value']

        # 选择最近的数据进行热力图显示
        recent_dates = df['date'].unique()[-10:]  # 最近10个交易日
        df_recent = df[df['date'].isin(recent_dates)]

        pivot_table = df_recent.pivot(index='asset', columns='date', values='factor_value')

        ax = self.figure.add_subplot(111)

        # 使用imshow绘制热力图
        im = ax.imshow(pivot_table.values, cmap='RdYlBu_r', aspect='auto')

        # 设置坐标轴
        ax.set_xticks(range(len(pivot_table.columns)))
        ax.set_xticklabels([str(d)[:10] for d in pivot_table.columns], rotation=45)
        ax.set_yticks(range(len(pivot_table.index)))
        ax.set_yticklabels(pivot_table.index)

        font_props = get_chinese_font_props()
        if CHINESE_FONT_AVAILABLE and font_props:
            ax.set_title('因子热力图 (最近10个交易日)', fontsize=14, fontweight='bold', fontproperties=font_props)
            ax.set_xlabel('日期', fontsize=10, fontproperties=font_props)
            ax.set_ylabel('股票代码', fontsize=10, fontproperties=font_props)
        else:
            ax.set_title('Factor Heatmap (Recent 10 Trading Days)', fontsize=14, fontweight='bold')
            ax.set_xlabel('Date', fontsize=10)
            ax.set_ylabel('Stock Code', fontsize=10)

        # 添加颜色条
        cbar = self.figure.colorbar(im, ax=ax)
        if CHINESE_FONT_AVAILABLE and font_props:
            cbar.set_label('因子值', fontsize=10, fontproperties=font_props)
        else:
            cbar.set_label('Factor Value', fontsize=10)

    def _plot_return_analysis(self):
        """绘制收益分析图"""
        # Check if quantile_returns data is available using proper DataFrame/dict checks
        quantile_returns_available = False
        if (self.current_analysis is not None and
            'quantile_returns' in self.current_analysis):
            quantile_returns = self.current_analysis['quantile_returns']
            # Check if quantile_returns is not None and not empty
            if quantile_returns is not None:
                if hasattr(quantile_returns, 'empty'):
                    # It's a DataFrame/Series
                    quantile_returns_available = not quantile_returns.empty
                elif isinstance(quantile_returns, dict):
                    # It's a dictionary
                    quantile_returns_available = bool(quantile_returns)
                else:
                    # Other types
                    quantile_returns_available = bool(quantile_returns)

        if not quantile_returns_available:
            ax = self.figure.add_subplot(111)
            if CHINESE_FONT_AVAILABLE:
                error_text = '收益分析数据不可用\n需要完整的因子分析结果'
            else:
                error_text = 'Return Analysis Data Unavailable\nComplete factor analysis results required'
            # 设置收益分析错误文本的字体
            if CHINESE_FONT_AVAILABLE:
                font_props = get_chinese_font_props()
                if font_props:
                    ax.text(0.5, 0.5, error_text,
                            ha='center', va='center', fontsize=14, color='gray',
                            transform=ax.transAxes, fontproperties=font_props)
                else:
                    ax.text(0.5, 0.5, error_text,
                            ha='center', va='center', fontsize=14, color='gray',
                            transform=ax.transAxes, fontfamily=['SimHei', 'Microsoft YaHei', 'SimSun'])
            else:
                ax.text(0.5, 0.5, error_text,
                        ha='center', va='center', fontsize=14, color='gray',
                        transform=ax.transAxes)
            return

        # 获取分位数收益数据
        quantile_returns = self.current_analysis['quantile_returns']

        # 创建分位数收益图表
        ax = self.figure.add_subplot(111)

        # 绘制分位数收益柱状图
        if 'mean_returns_by_quantile' in quantile_returns:
            mean_returns = quantile_returns['mean_returns_by_quantile']

            # 如果是Series，转换为DataFrame
            if isinstance(mean_returns, pd.Series):
                mean_returns = mean_returns.to_frame('1D')

            # 绘制柱状图 - 使用proper DataFrame empty check
            if not mean_returns.empty:
                # 选择第一个周期的均值数据进行展示
                # 优先选择包含'mean'的列，如果没有则选择第一列
                mean_cols = [col for col in mean_returns.columns if 'mean' in col.lower()]
                if mean_cols:
                    period_col = mean_cols[0]  # 选择第一个均值列
                else:
                    period_col = mean_returns.columns[0]  # 备用方案

                returns_data = mean_returns[period_col] * 100  # 转换为百分比

                bars = ax.bar(range(len(returns_data)), returns_data,
                             color=['#f44336' if x < 0 else '#4CAF50' for x in returns_data],
                             alpha=0.7, edgecolor='black', linewidth=0.5)

                # 设置x轴标签
                ax.set_xticks(range(len(returns_data)))
                ax.set_xticklabels([f'Q{i+1}' for i in range(len(returns_data))])

                # 在柱子上显示数值
                for bar, value in zip(bars, returns_data):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + (0.1 if height >= 0 else -0.3),
                           f'{value:.2f}%', ha='center', va='bottom' if height >= 0 else 'top',
                           fontsize=9, fontweight='bold')
            else:
                # 如果没有数据，显示提示信息
                ax.text(0.5, 0.5, '暂无分位数收益数据', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14, color='gray')
        else:
            # 显示模拟数据作为备用
            dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
            returns = np.random.normal(0.001, 0.02, 100)
            cumulative_returns = np.cumprod(1 + returns) - 1

            ax.plot(dates, cumulative_returns * 100, linewidth=2, color='#4CAF50')
            ax.fill_between(dates, cumulative_returns * 100, alpha=0.3, color='#4CAF50')

        # 设置图表标题和标签
        font_props = get_chinese_font_props()
        if CHINESE_FONT_AVAILABLE and font_props:
            if 'mean_returns_by_quantile' in quantile_returns:
                ax.set_title('因子分位数收益分析', fontsize=14, fontweight='bold', fontproperties=font_props)
                ax.set_xlabel('分位数', fontsize=10, fontproperties=font_props)
                ax.set_ylabel('平均收益率 (%)', fontsize=10, fontproperties=font_props)
            else:
                ax.set_title('因子收益分析 (示例)', fontsize=14, fontweight='bold', fontproperties=font_props)
                ax.set_xlabel('日期', fontsize=10, fontproperties=font_props)
                ax.set_ylabel('累积收益率 (%)', fontsize=10, fontproperties=font_props)
        else:
            if 'mean_returns_by_quantile' in quantile_returns:
                ax.set_title('Factor Quantile Returns Analysis', fontsize=14, fontweight='bold')
                ax.set_xlabel('Quantile', fontsize=10)
                ax.set_ylabel('Mean Return (%)', fontsize=10)
            else:
                ax.set_title('Factor Return Analysis (Example)', fontsize=14, fontweight='bold')
                ax.set_xlabel('Date', fontsize=10)
                ax.set_ylabel('Cumulative Return (%)', fontsize=10)
        ax.grid(True, alpha=0.3)

        # 只有在显示时序数据时才格式化日期
        if 'mean_returns_by_quantile' not in quantile_returns:
            # 这是时序图，需要日期格式化
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            self.figure.autofmt_xdate()
        # 分位数柱状图不需要日期格式化

    def _show_error_chart(self, error_msg):
        """显示错误信息"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)

        if CHINESE_FONT_AVAILABLE:
            error_text = f'图表生成错误:\n{error_msg}'
        else:
            error_text = f'Chart Generation Error:\n{error_msg}'

        # 设置错误消息文本的字体
        if CHINESE_FONT_AVAILABLE:
            font_props = get_chinese_font_props()
            if font_props:
                ax.text(0.5, 0.5, error_text,
                        ha='center', va='center', fontsize=12, color='red',
                        transform=ax.transAxes, fontproperties=font_props)
            else:
                ax.text(0.5, 0.5, error_text,
                        ha='center', va='center', fontsize=12, color='red',
                        transform=ax.transAxes, fontfamily=['SimHei', 'Microsoft YaHei', 'SimSun'])
        else:
            ax.text(0.5, 0.5, error_text,
                    ha='center', va='center', fontsize=12, color='red',
                    transform=ax.transAxes)
        ax.set_xticks([])
        ax.set_yticks([])
        self.canvas.draw()

    def _export_chart(self):
        """导出图表"""
        if not MATPLOTLIB_AVAILABLE or not self.figure:
            return

        from PyQt5.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存图表", f"factor_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            "PNG文件 (*.png);;PDF文件 (*.pdf);;SVG文件 (*.svg)"
        )

        if file_path:
            try:
                self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                QMessageBox.information(self, "成功", f"图表已保存到：\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存图表失败：\n{str(e)}")


class DataDownloadThread(QThread):
    """数据下载线程"""
    progress_updated = pyqtSignal(int)
    download_finished = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_manage, missing_assets, start_date, end_date, factor_name, params, all_assets):
        super().__init__()
        self.db_manage = db_manage
        self.missing_assets = missing_assets
        self.start_date = start_date
        self.end_date = end_date
        self.factor_name = factor_name
        self.params = params
        self.all_assets = all_assets

    def run(self):
        try:
            self.progress_updated.emit(15)

            # 使用data_engine下载数据
            if hasattr(self.db_manage, 'data_engine'):
                data_engine = self.db_manage.data_engine

                total_assets = len(self.missing_assets)
                success_count = 0

                for i, asset in enumerate(self.missing_assets):
                    try:
                        # 下载并清洗数据
                        cleaned_data = data_engine.download_and_clean_data(
                            symbol=asset,
                            start_date=self.start_date,
                            end_date=self.end_date
                        )

                        if not cleaned_data.empty:
                            # 保存到数据库
                            self.db_manage.save_stock_data(cleaned_data, asset)
                            success_count += 1
                            logger.info(f"Successfully downloaded and saved data for {asset}")
                        else:
                            logger.warning(f"No data downloaded for {asset}")

                        # 更新进度
                        progress = 15 + int((i + 1) / total_assets * 35)  # 15-50%
                        self.progress_updated.emit(progress)

                        # 短暂延迟，避免请求过于频繁
                        import time
                        time.sleep(0.5)

                    except Exception as e:
                        logger.error(f"Error downloading data for {asset}: {e}")

                self.progress_updated.emit(50)

                # 返回下载结果
                self.download_finished.emit({
                    'success_count': success_count,
                    'total_count': total_assets,
                    'factor_name': self.factor_name,
                    'params': self.params,
                    'all_assets': self.all_assets,
                    'start_date': self.start_date,
                    'end_date': self.end_date
                })

            else:
                self.error_occurred.emit("数据引擎不可用")

        except Exception as e:
            self.error_occurred.emit(str(e))


class FactorCalculationThread(QThread):
    """因子计算线程"""
    progress_updated = pyqtSignal(int)
    calculation_finished = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, factor_engine, factor_analyzer, factor_name, assets, start_date, end_date, params):
        super().__init__()
        self.factor_engine = factor_engine
        self.factor_analyzer = factor_analyzer
        self.factor_name = factor_name
        self.assets = assets
        self.start_date = start_date
        self.end_date = end_date
        self.params = params

    def run(self):
        try:
            self.progress_updated.emit(10)

            # 计算因子
            factor_data = self.factor_engine.calculate_factor(
                factor_name=self.factor_name,
                assets=self.assets,
                start_date=self.start_date,
                end_date=self.end_date,
                **self.params
            )
            self.progress_updated.emit(50)

            # 分析因子
            results = self.factor_analyzer.analyze_factor(factor_data)
            self.progress_updated.emit(90)

            self.progress_updated.emit(100)
            self.calculation_finished.emit({
                'factor_data': factor_data,
                'analysis_results': results,
                'factor_name': self.factor_name
            })

        except Exception as e:
            self.error_occurred.emit(str(e))


class PanelFactorResearch(QWidget):
    """因子研究面板"""
    
    def __init__(self, parent=None, db_manage=None):
        super().__init__(parent)
        self.db_manage = db_manage
        self.factor_engine = None
        self.factor_analyzer = None
        self.data_adapter = None
        self.visualizer = None
        self.current_results = None

        # 初始化因子研究框架
        self._init_factor_research()
        
        # 初始化UI
        self._init_ui()
        
        # 初始化数据
        self._init_data()
        
    def _init_factor_research(self):
        """初始化因子研究框架（简化版）"""
        if not FACTOR_RESEARCH_AVAILABLE:
            return

        try:
            # 初始化数据适配器
            self.data_adapter = DataAdapter(db_manage=self.db_manage)

            # 初始化因子计算引擎
            self.factor_engine = FactorEngine(data_adapter=self.data_adapter)

            # 注册默认因子
            self.factor_engine.register_factor(MomentumFactor, lookback_period=20)
            self.factor_engine.register_factor(RSIFactor, period=14)
            self.factor_engine.register_factor(PriceVolumeTrendFactor, period=20)

            # 初始化因子分析器
            self.factor_analyzer = FactorAnalyzer(db_manage=self.db_manage)

            # 初始化可视化器
            self.visualizer = FactorVisualizer()

            logger.info("Factor research framework initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize factor research framework: {e}")
            self.factor_engine = None
            self.factor_analyzer = None
            self.data_adapter = None
            self.visualizer = None
    
    def _init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        main_layout.setSpacing(3)  # 减少组件间距
        
        # 标题 - 适中布局
        title_label = QLabel("📈 因子研究")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))  # 增加字体大小
        title_label.setMaximumHeight(35)  # 增加最大高度
        title_label.setMinimumHeight(35)  # 增加最小高度
        title_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        title_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 6px 12px;  /* 增加内边距 */
                margin: 3px 0px;
                font-size: 12px;    /* 确保字体大小 */
                line-height: 1.2;   /* 设置行高 */
            }
        """)
        main_layout.addWidget(title_label)
        
        if not FACTOR_RESEARCH_AVAILABLE:
            # 显示错误信息
            error_label = QLabel("因子研究框架不可用，请检查安装")
            error_label.setStyleSheet("color: red; font-size: 14px;")
            main_layout.addWidget(error_label)
            self.setLayout(main_layout)
            return
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧控制面板
        control_panel = self._create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧结果面板
        result_panel = self._create_result_panel()
        splitter.addWidget(result_panel)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
    
    def _create_control_panel(self):
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # 因子选择组 - 使用自定义标题和边框
        factor_container = QVBoxLayout()
        factor_container.setSpacing(2)

        # 因子配置标题
        factor_title = QLabel("📈 因子配置")
        factor_title.setFont(QFont("Arial", 12, QFont.Bold))
        factor_title.setStyleSheet("""
            QLabel {
                color: #333333;
                background-color: transparent;
                padding: 5px 10px;
                margin: 0px;
            }
        """)
        factor_container.addWidget(factor_title)

        # 因子配置内容框
        factor_frame = QFrame()
        factor_frame.setFrameStyle(QFrame.StyledPanel)
        factor_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                background-color: white;
                margin: 0px;
            }
        """)

        factor_layout = QVBoxLayout()
        factor_layout.setSpacing(8)  # 增加组件间距
        factor_layout.setContentsMargins(12, 10, 12, 10)  # 标准边距

        # 因子类型选择 - 水平布局
        factor_type_layout = QHBoxLayout()
        factor_label = QLabel("类型:")
        factor_label.setFixedWidth(50)  # 稍微增加标签宽度
        factor_label.setStyleSheet("font-weight: bold;")

        self.factor_combo = QComboBox()
        # 使用更简洁的因子名称
        self.factor_combo.addItems(["动量因子", "RSI因子", "价量趋势"])
        self.factor_combo.setMinimumHeight(35)  # 增加最小高度
        self.factor_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        factor_type_layout.addWidget(factor_label)
        factor_type_layout.addWidget(self.factor_combo)
        factor_type_layout.addStretch()  # 添加弹性空间

        factor_layout.addLayout(factor_type_layout)

        # 因子参数区域
        self.param_layout = QVBoxLayout()
        self.param_layout.setContentsMargins(0, 8, 0, 5)  # 调整参数区域边距
        self.param_layout.setSpacing(5)
        self._update_factor_params()
        factor_layout.addLayout(self.param_layout)

        self.factor_combo.currentTextChanged.connect(self._update_factor_params)
        factor_frame.setLayout(factor_layout)
        factor_frame.setMinimumHeight(100)  # 设置最小高度
        factor_frame.setMaximumHeight(150)  # 适当放宽最大高度限制
        factor_container.addWidget(factor_frame)

        # 将因子容器添加到主布局
        factor_widget = QWidget()
        factor_widget.setLayout(factor_container)
        layout.addWidget(factor_widget)
        
        # 股票池选择组 - 使用自定义标题和边框
        stock_container = QVBoxLayout()
        stock_container.setSpacing(2)

        # 股票池标题
        stock_title = QLabel("🏢 股票池")
        stock_title.setFont(QFont("Arial", 12, QFont.Bold))
        stock_title.setStyleSheet("""
            QLabel {
                color: #333333;
                background-color: transparent;
                padding: 5px 10px;
                margin: 0px;
            }
        """)
        stock_container.addWidget(stock_title)

        # 股票池内容框
        stock_frame = QFrame()
        stock_frame.setFrameStyle(QFrame.StyledPanel)
        stock_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                background-color: white;
                margin: 0px;
            }
        """)

        stock_main_layout = QVBoxLayout()
        stock_main_layout.setSpacing(8)
        stock_main_layout.setContentsMargins(12, 10, 12, 10)  # 标准边距

        # 第一行：股票代码输入
        stock_input_layout = QHBoxLayout()
        stock_label = QLabel("股票代码:")
        stock_label.setFixedWidth(80)
        stock_label.setStyleSheet("font-weight: bold;")

        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText("输入股票代码，用逗号分隔，如: 000001,000002,600000")
        self.stock_input.setText("000001,000002,600000")
        self.stock_input.setMinimumHeight(35)
        self.stock_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)
        # 监听文本变化，自动更新数据状态
        self.stock_input.textChanged.connect(self._on_stock_input_changed)

        stock_input_layout.addWidget(stock_label)
        stock_input_layout.addWidget(self.stock_input)

        # 第二行：快速选择按钮
        stock_button_layout = QHBoxLayout()
        quick_select_btn = QPushButton("📋 快速选择常用股票池")
        quick_select_btn.setMinimumHeight(30)
        quick_select_btn.setToolTip("选择预设的股票池")
        quick_select_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        quick_select_btn.clicked.connect(self._show_quick_select)

        # 添加沪深300快速选择按钮
        hs300_btn = QPushButton("📈 沪深300")
        hs300_btn.setMinimumHeight(30)
        hs300_btn.setToolTip("选择沪深300成分股（核心股票池）")
        hs300_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        hs300_btn.clicked.connect(self._select_hs300_stocks)

        stock_button_layout.addWidget(quick_select_btn)
        stock_button_layout.addWidget(hs300_btn)
        stock_button_layout.addStretch()

        # 第三行：数据状态显示
        data_status_layout = QHBoxLayout()
        self.data_status_label = QLabel("💾 数据状态: 等待检查...")
        self.data_status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 11px;
                padding: 2px 5px;
                background-color: #f8f8f8;
                border-radius: 3px;
            }
        """)
        data_status_layout.addWidget(self.data_status_label)
        data_status_layout.addStretch()

        stock_main_layout.addLayout(stock_input_layout)
        stock_main_layout.addLayout(stock_button_layout)
        stock_main_layout.addLayout(data_status_layout)

        stock_frame.setLayout(stock_main_layout)
        stock_frame.setMinimumHeight(90)
        stock_frame.setMaximumHeight(120)
        stock_container.addWidget(stock_frame)

        # 将股票池容器添加到主布局
        stock_widget = QWidget()
        stock_widget.setLayout(stock_container)
        layout.addWidget(stock_widget)
        
        # 时间范围组 - 使用自定义标题和边框
        time_container = QVBoxLayout()
        time_container.setSpacing(2)

        # 时间范围标题
        time_title = QLabel("📅 时间范围")
        time_title.setFont(QFont("Arial", 12, QFont.Bold))
        time_title.setStyleSheet("""
            QLabel {
                color: #333333;
                background-color: transparent;
                padding: 5px 10px;
                margin: 0px;
            }
        """)
        time_container.addWidget(time_title)

        # 时间范围内容框
        time_frame = QFrame()
        time_frame.setFrameStyle(QFrame.StyledPanel)
        time_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                background-color: white;
                margin: 0px;
            }
        """)

        time_main_layout = QVBoxLayout()
        time_main_layout.setSpacing(8)
        time_main_layout.setContentsMargins(12, 10, 12, 10)  # 标准边距

        # 第一行：日期选择
        time_layout = QHBoxLayout()
        time_layout.setSpacing(15)

        # 开始日期
        start_label = QLabel("开始日期:")
        start_label.setFixedWidth(70)
        start_label.setStyleSheet("font-weight: bold;")

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addYears(-1))
        self.start_date.setCalendarPopup(True)
        self.start_date.setMinimumHeight(35)
        self.start_date.setDisplayFormat("yyyy-MM-dd")
        self.start_date.setStyleSheet("""
            QDateEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        # 结束日期
        end_label = QLabel("结束日期:")
        end_label.setFixedWidth(70)
        end_label.setStyleSheet("font-weight: bold;")

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        self.end_date.setMinimumHeight(35)
        self.end_date.setDisplayFormat("yyyy-MM-dd")
        self.end_date.setStyleSheet("""
            QDateEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        time_layout.addWidget(start_label)
        time_layout.addWidget(self.start_date)
        time_layout.addWidget(end_label)
        time_layout.addWidget(self.end_date)
        time_layout.addStretch()

        # 第二行：快速时间选择
        quick_time_layout = QHBoxLayout()
        quick_time_label = QLabel("快速选择:")
        quick_time_label.setStyleSheet("font-weight: bold; color: #666;")
        quick_time_layout.addWidget(quick_time_label)

        # 快速时间按钮
        time_buttons = [
            ("近1月", 30),
            ("近3月", 90),
            ("近6月", 180),
            ("近1年", 365)
        ]

        for text, days in time_buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(25)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    padding: 3px 8px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)
            btn.clicked.connect(lambda _, d=days: self._set_quick_time_range(d))
            quick_time_layout.addWidget(btn)

        quick_time_layout.addStretch()

        time_main_layout.addLayout(time_layout)
        time_main_layout.addLayout(quick_time_layout)

        time_frame.setLayout(time_main_layout)
        time_frame.setMinimumHeight(100)
        time_frame.setMaximumHeight(130)
        time_container.addWidget(time_frame)

        # 将时间范围容器添加到主布局
        time_widget = QWidget()
        time_widget.setLayout(time_container)
        layout.addWidget(time_widget)
        
        # 控制按钮 - 水平布局
        button_layout = QHBoxLayout()

        self.calculate_btn = QPushButton("🔍 计算因子")
        self.calculate_btn.clicked.connect(self._calculate_factor)
        self.calculate_btn.setMinimumHeight(35)
        self.calculate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

        self.export_btn = QPushButton("📊 导出报告")
        self.export_btn.clicked.connect(self._export_report)
        self.export_btn.setEnabled(False)
        self.export_btn.setMinimumHeight(35)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)

        button_layout.addWidget(self.calculate_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.setContentsMargins(10, 5, 10, 5)

        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        panel.setLayout(layout)
        return panel
    
    def _create_result_panel(self):
        """创建结果面板"""
        panel = QTabWidget()
        
        # 结果概览标签页
        overview_tab = QWidget()
        overview_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setPlaceholderText("因子分析结果将在这里显示...")
        overview_layout.addWidget(self.result_text)
        
        overview_tab.setLayout(overview_layout)
        panel.addTab(overview_tab, "分析结果")
        
        # 数据表格标签页
        data_tab = QWidget()
        data_layout = QVBoxLayout()
        
        self.data_table = QTableWidget()
        data_layout.addWidget(self.data_table)
        
        data_tab.setLayout(data_layout)
        panel.addTab(data_tab, "因子数据")
        
        # 图表标签页
        chart_tab = QWidget()
        chart_layout = QVBoxLayout()
        chart_layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表组件
        self.chart_widget = FactorChartWidget()
        chart_layout.addWidget(self.chart_widget)

        chart_tab.setLayout(chart_layout)
        panel.addTab(chart_tab, "可视化")
        
        return panel

    def _update_factor_params(self):
        """更新因子参数界面"""
        # 清除现有参数控件
        for i in reversed(range(self.param_layout.count())):
            child = self.param_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        factor_name = self.factor_combo.currentText()

        if factor_name == "动量因子":
            # 动量因子参数 - 水平布局
            momentum_layout = QHBoxLayout()

            lookback_label = QLabel("回看:")
            lookback_label.setFixedWidth(40)
            self.lookback_spin = QSpinBox()
            self.lookback_spin.setRange(1, 252)
            self.lookback_spin.setValue(20)
            self.lookback_spin.setMaximumHeight(25)
            self.lookback_spin.setSuffix("天")

            skip_label = QLabel("跳过:")
            skip_label.setFixedWidth(40)
            self.skip_spin = QSpinBox()
            self.skip_spin.setRange(0, 10)
            self.skip_spin.setValue(1)
            self.skip_spin.setMaximumHeight(25)
            self.skip_spin.setSuffix("天")

            momentum_layout.addWidget(lookback_label)
            momentum_layout.addWidget(self.lookback_spin)
            momentum_layout.addWidget(skip_label)
            momentum_layout.addWidget(self.skip_spin)
            momentum_layout.addStretch()

            self.param_layout.addLayout(momentum_layout)

        elif factor_name == "RSI因子":
            # RSI因子参数 - 水平布局
            rsi_layout = QHBoxLayout()

            rsi_label = QLabel("周期:")
            rsi_label.setFixedWidth(40)
            self.rsi_period_spin = QSpinBox()
            self.rsi_period_spin.setRange(2, 100)
            self.rsi_period_spin.setValue(14)
            self.rsi_period_spin.setMaximumHeight(25)
            self.rsi_period_spin.setSuffix("天")

            rsi_layout.addWidget(rsi_label)
            rsi_layout.addWidget(self.rsi_period_spin)
            rsi_layout.addStretch()

            self.param_layout.addLayout(rsi_layout)

        elif factor_name == "价量趋势":
            # 价量趋势因子参数 - 水平布局
            pvt_layout = QHBoxLayout()

            pvt_label = QLabel("周期:")
            pvt_label.setFixedWidth(40)
            self.pvt_period_spin = QSpinBox()
            self.pvt_period_spin.setRange(1, 100)
            self.pvt_period_spin.setValue(20)
            self.pvt_period_spin.setMaximumHeight(25)
            self.pvt_period_spin.setSuffix("天")

            pvt_layout.addWidget(pvt_label)
            pvt_layout.addWidget(self.pvt_period_spin)
            pvt_layout.addStretch()

            self.param_layout.addLayout(pvt_layout)

    def _init_data(self):
        """初始化数据"""
        pass

    def _get_factor_class_name(self, display_name):
        """将显示名称映射为实际的因子类名"""
        factor_mapping = {
            "动量因子": "MomentumFactor",
            "RSI因子": "RSIFactor",
            "价量趋势": "PriceVolumeTrendFactor"
        }
        return factor_mapping.get(display_name, display_name)

    def _calculate_factor(self):
        """计算因子"""
        if not self.factor_engine or not self.factor_analyzer:
            QMessageBox.warning(self, "错误", "因子研究框架未初始化")
            return

        # 获取参数
        factor_display_name = self.factor_combo.currentText()
        factor_name = self._get_factor_class_name(factor_display_name)  # 转换为实际类名

        assets_text = self.stock_input.text().strip()
        if not assets_text:
            QMessageBox.warning(self, "错误", "请输入股票代码")
            return

        assets = [code.strip() for code in assets_text.split(',') if code.strip()]
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")

        # 获取因子参数
        params = self._get_factor_params()

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.calculate_btn.setEnabled(False)

        # 在计算因子前先检查并下载缺失数据
        self._check_and_download_missing_data(assets, start_date, end_date, factor_name, params)

    def _check_and_download_missing_data(self, assets, start_date, end_date, factor_name, params):
        """检查并下载缺失数据"""
        try:
            # 更新进度和状态
            self.progress_bar.setValue(5)
            self.data_status_label.setText("💾 数据状态: 检查数据完整性...")

            # 检查数据可用性
            missing_assets = self._check_data_availability(assets, start_date, end_date)

            if missing_assets:
                # 询问用户是否下载缺失数据
                reply = QMessageBox.question(
                    self,
                    "数据缺失",
                    f"检测到以下股票缺少价格数据:\n{', '.join(missing_assets)}\n\n"
                    f"是否自动下载这些股票的历史数据?\n"
                    f"下载可能需要几分钟时间。",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # 启动数据下载线程
                    self._start_data_download(missing_assets, start_date, end_date, factor_name, params, assets)
                else:
                    # 用户选择不下载，直接计算（可能会有警告）
                    self._start_factor_calculation(factor_name, assets, start_date, end_date, params)
            else:
                # 数据完整，直接计算
                self.data_status_label.setText("💾 数据状态: 数据完整，开始计算...")
                self._start_factor_calculation(factor_name, assets, start_date, end_date, params)

        except Exception as e:
            logger.error(f"Error checking data availability: {e}")
            # 如果检查失败，直接计算
            self._start_factor_calculation(factor_name, assets, start_date, end_date, params)

    def _check_data_availability(self, assets, start_date, end_date):
        """检查数据可用性，返回缺失数据的股票列表"""
        missing_assets = []

        try:
            if self.data_adapter and hasattr(self.data_adapter, 'data_engine'):
                data_engine = self.data_adapter.data_engine

                # 检查每个股票的数据
                for asset in assets:
                    try:
                        # 尝试获取价格数据
                        price_data = data_engine._get_raw_price_data_from_db([asset], start_date, end_date)

                        if price_data.empty:
                            missing_assets.append(asset)
                            logger.info(f"No price data found for {asset}")
                        else:
                            # 检查数据量是否足够（至少需要一些数据点）
                            if len(price_data) < 10:  # 少于10个数据点认为数据不足
                                missing_assets.append(asset)
                                logger.info(f"Insufficient price data for {asset}: only {len(price_data)} records")

                    except Exception as e:
                        logger.warning(f"Error checking data for {asset}: {e}")
                        missing_assets.append(asset)

        except Exception as e:
            logger.error(f"Error in data availability check: {e}")
            # 如果检查过程出错，假设所有数据都缺失（安全起见）
            missing_assets = assets

        return missing_assets

    def _start_data_download(self, missing_assets, start_date, end_date, factor_name, params, all_assets):
        """启动数据下载线程"""
        try:
            self.data_status_label.setText(f"💾 数据状态: 正在下载{len(missing_assets)}只股票的数据...")
            self.progress_bar.setValue(10)

            # 启动下载线程
            self.download_thread = DataDownloadThread(
                self.db_manage, missing_assets, start_date, end_date,
                factor_name, params, all_assets
            )
            self.download_thread.progress_updated.connect(self._on_download_progress)
            self.download_thread.download_finished.connect(self._on_download_finished)
            self.download_thread.error_occurred.connect(self._on_download_error)
            self.download_thread.start()

        except Exception as e:
            logger.error(f"Error starting data download: {e}")
            QMessageBox.critical(self, "错误", f"启动数据下载失败：{str(e)}")
            self._reset_ui_state()

    def _start_factor_calculation(self, factor_name, assets, start_date, end_date, params):
        """启动因子计算线程"""
        try:
            # 启动计算线程
            self.calc_thread = FactorCalculationThread(
                self.factor_engine, self.factor_analyzer, factor_name, assets, start_date, end_date, params
            )
            self.calc_thread.progress_updated.connect(self.progress_bar.setValue)
            self.calc_thread.calculation_finished.connect(self._on_calculation_finished)
            self.calc_thread.error_occurred.connect(self._on_calculation_error)
            self.calc_thread.start()

        except Exception as e:
            logger.error(f"Error starting factor calculation: {e}")
            QMessageBox.critical(self, "错误", f"启动因子计算失败：{str(e)}")
            self._reset_ui_state()

    def _on_download_progress(self, progress):
        """下载进度更新"""
        self.progress_bar.setValue(progress)

    def _on_download_finished(self, result):
        """下载完成回调"""
        try:
            success_count = result['success_count']
            total_count = result['total_count']

            # 更新状态显示
            if success_count == total_count:
                self.data_status_label.setText(f"💾 数据状态: 成功下载{success_count}只股票数据")
                self.data_status_label.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        font-size: 11px;
                        padding: 2px 5px;
                        background-color: #e8f5e8;
                        border-radius: 3px;
                    }
                """)
            else:
                self.data_status_label.setText(f"💾 数据状态: 下载完成({success_count}/{total_count})")
                self.data_status_label.setStyleSheet("""
                    QLabel {
                        color: #FF9800;
                        font-size: 11px;
                        padding: 2px 5px;
                        background-color: #fff3e0;
                        border-radius: 3px;
                    }
                """)

            # 显示下载结果
            if success_count > 0:
                QMessageBox.information(
                    self,
                    "下载完成",
                    f"数据下载完成！\n"
                    f"成功下载: {success_count}/{total_count} 只股票\n"
                    f"现在开始计算因子..."
                )

                # 下载完成后，开始因子计算
                self._start_factor_calculation(
                    result['factor_name'],
                    result['all_assets'],
                    result['start_date'],
                    result['end_date'],
                    result['params']
                )
            else:
                QMessageBox.warning(
                    self,
                    "下载失败",
                    f"未能成功下载任何股票数据。\n"
                    f"请检查网络连接或股票代码是否正确。\n"
                    f"将尝试使用现有数据进行计算..."
                )

                # 即使下载失败，也尝试计算（可能会有警告）
                self._start_factor_calculation(
                    result['factor_name'],
                    result['all_assets'],
                    result['start_date'],
                    result['end_date'],
                    result['params']
                )

        except Exception as e:
            logger.error(f"Error in download finished callback: {e}")
            self._on_download_error(str(e))

    def _on_download_error(self, error_msg):
        """下载错误回调"""
        logger.error(f"Data download error: {error_msg}")

        self.data_status_label.setText("💾 数据状态: 下载失败")
        self.data_status_label.setStyleSheet("""
            QLabel {
                color: #f44336;
                font-size: 11px;
                padding: 2px 5px;
                background-color: #ffebee;
                border-radius: 3px;
            }
        """)

        QMessageBox.critical(
            self,
            "下载错误",
            f"数据下载失败：\n{error_msg}\n\n"
            f"将尝试使用现有数据进行计算..."
        )

        # 重置UI状态
        self._reset_ui_state()

    def _reset_ui_state(self):
        """重置UI状态"""
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)
        self.calculate_btn.setEnabled(True)

    def _get_factor_params(self):
        """获取因子参数"""
        factor_name = self.factor_combo.currentText()
        params = {}

        if factor_name == "动量因子":
            params['lookback_period'] = self.lookback_spin.value()
            params['skip_period'] = self.skip_spin.value()
        elif factor_name == "RSI因子":
            params['period'] = self.rsi_period_spin.value()
        elif factor_name == "价量趋势":
            params['period'] = self.pvt_period_spin.value()

        return params

    def _on_calculation_finished(self, results):
        """计算完成回调"""
        self.current_results = results

        # 重置UI状态
        self._reset_ui_state()
        self.export_btn.setEnabled(True)

        # 更新数据状态显示
        self.data_status_label.setText("💾 数据状态: 计算完成")
        self.data_status_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-size: 11px;
                padding: 2px 5px;
                background-color: #e8f5e8;
                border-radius: 3px;
            }
        """)

        # 显示结果
        self._display_results(results)

        QMessageBox.information(self, "完成", "因子计算和分析完成！")

    def _on_calculation_error(self, error_msg):
        """计算错误回调"""
        logger.error(f"Factor calculation error: {error_msg}")

        # 重置UI状态
        self._reset_ui_state()

        # 更新数据状态显示
        self.data_status_label.setText("💾 数据状态: 计算失败")
        self.data_status_label.setStyleSheet("""
            QLabel {
                color: #f44336;
                font-size: 11px;
                padding: 2px 5px;
                background-color: #ffebee;
                border-radius: 3px;
            }
        """)

        QMessageBox.critical(self, "计算错误", f"因子计算失败：\n{error_msg}")

    def _display_results(self, results):
        """显示分析结果"""
        factor_data = results['factor_data']
        analysis_results = results['analysis_results']
        factor_name = results['factor_name']

        # 显示文本结果
        result_text = f"因子: {factor_name}\n"
        result_text += f"数据点数: {len(factor_data)}\n"
        result_text += f"时间范围: {factor_data.index.get_level_values(0).min()} 到 {factor_data.index.get_level_values(0).max()}\n"
        result_text += f"股票数量: {len(factor_data.index.get_level_values(1).unique())}\n\n"

        # 添加统计信息
        result_text += "因子统计:\n"
        result_text += f"均值: {factor_data.mean():.4f}\n"
        result_text += f"标准差: {factor_data.std():.4f}\n"
        result_text += f"最小值: {factor_data.min():.4f}\n"
        result_text += f"最大值: {factor_data.max():.4f}\n\n"

        # 添加分析结果
        if analysis_results:
            result_text += "IC分析:\n"

            # 优先使用summary_stats中的汇总数据
            if 'summary_stats' in analysis_results:
                summary_stats = analysis_results['summary_stats']
                ic_mean = summary_stats.get('ic_mean', 'N/A')
                ic_std = summary_stats.get('ic_std', 'N/A')
                ic_ir = summary_stats.get('ic_ir', 'N/A')

                # 格式化数值显示
                if ic_mean != 'N/A':
                    ic_mean = f"{ic_mean:.6f}"
                if ic_std != 'N/A':
                    ic_std = f"{ic_std:.6f}"
                if ic_ir != 'N/A':
                    ic_ir = f"{ic_ir:.6f}"

                result_text += f"IC均值: {ic_mean}\n"
                result_text += f"IC标准差: {ic_std}\n"
                result_text += f"信息比率: {ic_ir}\n"

            # 如果没有summary_stats，尝试从ic_summary DataFrame中提取
            elif 'ic_summary' in analysis_results:
                ic_summary = analysis_results['ic_summary']
                if hasattr(ic_summary, 'loc') and 'mean' in ic_summary.index:
                    # ic_summary是DataFrame
                    ic_mean = ic_summary.loc['mean'].iloc[0] if len(ic_summary.columns) > 0 else 'N/A'
                    ic_std = ic_summary.loc['std'].iloc[0] if 'std' in ic_summary.index and len(ic_summary.columns) > 0 else 'N/A'

                    # 计算信息比率
                    if ic_mean != 'N/A' and ic_std != 'N/A' and ic_std != 0:
                        ic_ir = ic_mean / ic_std
                        ic_ir = f"{ic_ir:.6f}"
                    else:
                        ic_ir = 'N/A'

                    result_text += f"IC均值: {ic_mean:.6f}\n"
                    result_text += f"IC标准差: {ic_std:.6f}\n"
                    result_text += f"信息比率: {ic_ir}\n"
                else:
                    # 如果是字典格式
                    result_text += f"IC均值: {ic_summary.get('IC Mean', 'N/A')}\n"
                    result_text += f"IC标准差: {ic_summary.get('IC Std', 'N/A')}\n"
                    result_text += f"信息比率: {ic_summary.get('IR', 'N/A')}\n"
            else:
                result_text += "IC均值: N/A\n"
                result_text += "IC标准差: N/A\n"
                result_text += "信息比率: N/A\n"

        self.result_text.setText(result_text)

        # 显示数据表格
        self._display_data_table(factor_data)

        # 更新图表
        self.chart_widget.update_data(factor_data, analysis_results)

    def _display_data_table(self, factor_data):
        """显示因子数据表格"""
        # 转换为DataFrame用于显示
        df = factor_data.reset_index()
        df.columns = ['日期', '股票代码', '因子值']

        # 设置表格
        self.data_table.setRowCount(min(len(df), 1000))  # 最多显示1000行
        self.data_table.setColumnCount(3)
        self.data_table.setHorizontalHeaderLabels(['日期', '股票代码', '因子值'])

        # 填充数据
        for i in range(min(len(df), 1000)):
            self.data_table.setItem(i, 0, QTableWidgetItem(str(df.iloc[i, 0])))
            self.data_table.setItem(i, 1, QTableWidgetItem(str(df.iloc[i, 1])))
            self.data_table.setItem(i, 2, QTableWidgetItem(f"{df.iloc[i, 2]:.4f}"))

        # 调整列宽
        self.data_table.resizeColumnsToContents()

    def _export_report(self):
        """导出分析报告"""
        if not self.current_results:
            QMessageBox.warning(self, "错误", "没有可导出的结果")
            return

        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存报告", f"factor_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            "HTML文件 (*.html)"
        )

        if file_path:
            try:
                # TODO: 实现简化版报告生成功能
                # 暂时保存为JSON格式
                import json
                with open(file_path.replace('.html', '.json'), 'w', encoding='utf-8') as f:
                    json.dump(self.current_results['analysis_results'], f,
                             ensure_ascii=False, indent=2, default=str)
                QMessageBox.information(self, "成功", f"分析结果已保存到：\n{file_path.replace('.html', '.json')}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出报告失败：\n{str(e)}")

    def _show_quick_select(self):
        """显示快速选择股票池对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QPushButton

        dialog = QDialog(self)
        dialog.setWindowTitle("快速选择股票池")
        dialog.setFixedSize(300, 250)

        layout = QVBoxLayout()

        # 预设股票池
        stock_pools = {
            "大盘蓝筹": "000001,000002,600000,600036,600519",
            "科技股": "000858,002415,300059,300750,688981",
            "银行股": "000001,600000,600036,601318,601398",
            "消费股": "000858,600519,000568,002304,603288",
            "测试池": "000001,000002,600000"
        }

        # 添加股票池按钮
        for name, codes in stock_pools.items():
            btn = QPushButton(f"{name} ({len(codes.split(','))}只)")
            btn.setToolTip(f"股票代码: {codes}")
            btn.clicked.connect(lambda _, c=codes: self._select_stock_pool(c, dialog))
            layout.addWidget(btn)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)
        layout.addWidget(cancel_btn)

        dialog.setLayout(layout)
        dialog.exec_()

    def _select_stock_pool(self, codes, dialog):
        """选择股票池"""
        self.stock_input.setText(codes)
        dialog.accept()
        self._update_data_status()

    def _select_hs300_stocks(self):
        """选择沪深300股票"""
        try:
            # 直接通过data_engine获取沪深300股票，避免不必要的adapter转发
            hs300_stocks = self._get_hs300_stocks_from_data_engine()

            if hs300_stocks:
                # 只选择前20只作为示例，避免界面过于拥挤
                selected_stocks = hs300_stocks[:20]
                self.stock_input.setText(','.join(selected_stocks))
                self._update_data_status()

                # 显示提示信息
                QMessageBox.information(
                    self,
                    "沪深300选择",
                    f"已选择沪深300前{len(selected_stocks)}只股票\n"
                    f"这些股票属于核心股票池，数据获取速度更快\n"
                    f"完整沪深300列表包含{len(hs300_stocks)}只股票"
                )
            else:
                QMessageBox.warning(self, "错误", "无法获取沪深300股票列表")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取沪深300股票失败：{str(e)}")

    def _get_hs300_stocks_from_data_engine(self) -> List[str]:
        """
        直接从data_engine获取沪深300股票列表

        避免通过adapter转发，保持架构简洁
        """
        try:
            # 1. 优先从data_engine的data_management_service获取
            if (self.data_adapter and
                hasattr(self.data_adapter, 'data_engine') and
                hasattr(self.data_adapter.data_engine, 'data_management_service') and
                self.data_adapter.data_engine.data_management_service):

                logger.debug("Getting HS300 stocks directly from data_engine.data_management_service")
                return self.data_adapter.data_engine.data_management_service.get_hs300_stocks()

            # 2. 备选：从db_manage获取
            if self.db_manage and hasattr(self.db_manage, '_get_hs300_stock_list'):
                logger.debug("Getting HS300 stocks from db_manage")
                return self.db_manage._get_hs300_stock_list()

            # 3. 最后的备选：返回默认列表
            logger.warning("Using default HS300 stock list")
            return [
                "000001", "000002", "600000", "600036", "600519", "000858",
                "002415", "300059", "300750", "688981", "000333", "000651",
                "002032", "002050", "600690", "000921", "002508", "600418"
            ]

        except Exception as e:
            logger.error(f"Error getting HS300 stocks from data_engine: {e}")
            # 返回基本股票列表
            return ["000001", "000002", "600000", "600036", "600519"]

    def _update_data_status(self):
        """更新数据状态显示"""
        try:
            assets_text = self.stock_input.text().strip()
            if not assets_text:
                self.data_status_label.setText("💾 数据状态: 请输入股票代码")
                self.data_status_label.setStyleSheet("""
                    QLabel {
                        color: #666666;
                        font-size: 11px;
                        padding: 2px 5px;
                        background-color: #f8f8f8;
                        border-radius: 3px;
                    }
                """)
                return

            assets = [code.strip() for code in assets_text.split(',') if code.strip()]
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            if self.data_adapter:
                # 获取数据状态信息
                status_info = self.data_adapter.get_data_strategy_info(assets, start_date, end_date)

                # 简化显示
                lines = status_info.split('\n')
                if len(lines) > 1:
                    summary = lines[1]  # 总股票数
                    if '需要下载' in status_info:
                        self.data_status_label.setText(f"⬇️ {summary}, 需要下载数据")
                        self.data_status_label.setStyleSheet("""
                            QLabel {
                                color: #ff9800;
                                font-size: 11px;
                                padding: 2px 5px;
                                background-color: #fff3e0;
                                border-radius: 3px;
                            }
                        """)
                    elif '核心股票池' in status_info:
                        self.data_status_label.setText(f"✅ {summary}, 核心股票池数据就绪")
                        self.data_status_label.setStyleSheet("""
                            QLabel {
                                color: #4caf50;
                                font-size: 11px;
                                padding: 2px 5px;
                                background-color: #e8f5e8;
                                border-radius: 3px;
                            }
                        """)
                    else:
                        self.data_status_label.setText(f"🚀 {summary}, 数据已就绪")
                        self.data_status_label.setStyleSheet("""
                            QLabel {
                                color: #2196f3;
                                font-size: 11px;
                                padding: 2px 5px;
                                background-color: #e3f2fd;
                                border-radius: 3px;
                            }
                        """)
                else:
                    self.data_status_label.setText("💾 数据状态: 检查中...")
            else:
                self.data_status_label.setText("⚠️ 数据状态: 框架未初始化")

        except Exception as e:
            self.data_status_label.setText(f"❌ 数据状态: 检查失败")
            logger.error(f"Error updating data status: {e}")

    def _on_stock_input_changed(self):
        """股票输入框文本变化时的处理"""
        # 使用定时器延迟更新，避免频繁更新
        if hasattr(self, '_update_timer'):
            self._update_timer.stop()

        from PyQt5.QtCore import QTimer
        self._update_timer = QTimer()
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self._update_data_status)
        self._update_timer.start(500)  # 500ms延迟

    def _set_quick_time_range(self, days):
        """设置快速时间范围"""
        from PyQt5.QtCore import QDate

        end_date = QDate.currentDate()
        start_date = end_date.addDays(-days)

        self.start_date.setDate(start_date)
        self.end_date.setDate(end_date)
