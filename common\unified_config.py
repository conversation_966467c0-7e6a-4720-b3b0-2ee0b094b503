"""
统一配置管理系统

整合所有配置管理功能，提供统一的配置接口。
遵循单一职责原则，简化配置管理复杂度。
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置"""
    base_dir: str = "data"
    quant_db_name: str = "quant_data.duckdb"
    hist_db_name: str = "hist_data.duckdb"
    realtime_db_name: str = "realtime_data.duckdb"
    snapshot_db_name: str = "snapshot_data.duckdb"
    backup_enabled: bool = True
    backup_interval_hours: int = 24


@dataclass
class DataEngineConfig:
    """数据引擎配置"""
    cache_enabled: bool = True
    cache_size_limit: int = 100
    max_workers: int = 4
    retry_times: int = 3
    request_timeout: int = 30
    rate_limit_per_second: int = 10


@dataclass
class FactorResearchConfig:
    """因子研究配置"""
    default_periods: list = None
    default_quantiles: int = 5
    max_loss: float = 0.35
    parallel_enabled: bool = False
    cache_enabled: bool = True
    alphalens_timeout: int = 300
    
    def __post_init__(self):
        if self.default_periods is None:
            self.default_periods = [1, 5, 10, 20]


@dataclass
class GUIConfig:
    """GUI配置"""
    window_width: int = 1400
    window_height: int = 900
    theme: str = "default"
    font_family: str = "SimHei"
    font_size: int = 10
    auto_refresh_interval: int = 60
    chart_dpi: int = 100


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    file_path: str = "logs/t_trade.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    console_enabled: bool = True


@dataclass
class TradingConfig:
    """交易配置"""
    default_account: str = "test"
    commission_rate: float = 0.0003
    stamp_duty_rate: float = 0.001
    min_commission: float = 5.0
    max_position_ratio: float = 0.95
    risk_control_enabled: bool = True


class UnifiedConfig:
    """
    统一配置管理器
    
    主要功能:
    - 配置文件加载和保存
    - 环境变量支持
    - 配置验证和默认值
    - 热重载支持
    - 配置备份和恢复
    """
    
    def __init__(self, config_file: str = "config/config.yaml"):
        """
        初始化配置管理器
        
        Parameters:
        -----------
        config_file : str
            配置文件路径
        """
        self.config_file = Path(config_file)
        self.config_dir = self.config_file.parent
        
        # 配置对象
        self.database = DatabaseConfig()
        self.data_engine = DataEngineConfig()
        self.factor_research = FactorResearchConfig()
        self.gui = GUIConfig()
        self.logging = LoggingConfig()
        self.trading = TradingConfig()
        
        # 内部状态
        self._last_modified = None
        self._watchers = []
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置
        self.load_config()
        
        logger.info(f"UnifiedConfig initialized with file: {self.config_file}")
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if self.config_file.suffix.lower() == '.yaml':
                        config_data = yaml.safe_load(f) or {}
                    else:
                        config_data = json.load(f)
                
                self._apply_config_data(config_data)
                self._last_modified = self.config_file.stat().st_mtime
                logger.info("Configuration loaded successfully")
            else:
                logger.info("Configuration file not found, using defaults")
                self.save_config()  # 创建默认配置文件
            
            # 应用环境变量覆盖
            self._apply_env_overrides()
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            logger.info("Using default configuration")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config_data = {
                'database': asdict(self.database),
                'data_engine': asdict(self.data_engine),
                'factor_research': asdict(self.factor_research),
                'gui': asdict(self.gui),
                'logging': asdict(self.logging),
                'trading': asdict(self.trading)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_file.suffix.lower() == '.yaml':
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self._last_modified = self.config_file.stat().st_mtime
            logger.info("Configuration saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def reload_if_changed(self):
        """如果配置文件已修改则重新加载"""
        try:
            if self.config_file.exists():
                current_mtime = self.config_file.stat().st_mtime
                if self._last_modified and current_mtime > self._last_modified:
                    logger.info("Configuration file changed, reloading...")
                    self.load_config()
                    self._notify_watchers()
                    return True
            return False
        except Exception as e:
            logger.error(f"Error checking configuration file: {e}")
            return False
    
    def get_database_path(self, db_name: str) -> str:
        """获取数据库文件路径"""
        return os.path.join(self.database.base_dir, db_name)
    
    def get_log_path(self) -> str:
        """获取日志文件路径"""
        log_path = Path(self.logging.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        return str(log_path)
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取完整配置字典"""
        return {
            'database': asdict(self.database),
            'data_engine': asdict(self.data_engine),
            'factor_research': asdict(self.factor_research),
            'gui': asdict(self.gui),
            'logging': asdict(self.logging),
            'trading': asdict(self.trading)
        }
    
    def update_config(self, section: str, **kwargs):
        """更新配置项"""
        try:
            if hasattr(self, section):
                config_obj = getattr(self, section)
                for key, value in kwargs.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
                        logger.debug(f"Updated {section}.{key} = {value}")
                    else:
                        logger.warning(f"Unknown config key: {section}.{key}")
            else:
                logger.warning(f"Unknown config section: {section}")
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
    
    def backup_config(self, backup_name: str = None):
        """备份配置文件"""
        try:
            if backup_name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"config_backup_{timestamp}"
            
            backup_path = self.config_dir / f"{backup_name}.yaml"
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.get_config_dict(), f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"Configuration backed up to: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"Error backing up configuration: {e}")
            return None
    
    def restore_config(self, backup_path: str):
        """从备份恢复配置"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                raise FileNotFoundError(f"Backup file not found: {backup_path}")
            
            with open(backup_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            self._apply_config_data(config_data)
            self.save_config()
            
            logger.info(f"Configuration restored from: {backup_path}")
            
        except Exception as e:
            logger.error(f"Error restoring configuration: {e}")
    
    def add_watcher(self, callback):
        """添加配置变更监听器"""
        self._watchers.append(callback)
    
    def remove_watcher(self, callback):
        """移除配置变更监听器"""
        if callback in self._watchers:
            self._watchers.remove(callback)
    
    def _apply_config_data(self, config_data: Dict[str, Any]):
        """应用配置数据"""
        for section_name, section_data in config_data.items():
            if hasattr(self, section_name) and isinstance(section_data, dict):
                config_obj = getattr(self, section_name)
                for key, value in section_data.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        # 数据库配置
        if 'T_TRADE_DB_DIR' in os.environ:
            self.database.base_dir = os.environ['T_TRADE_DB_DIR']
        
        # 日志配置
        if 'T_TRADE_LOG_LEVEL' in os.environ:
            self.logging.level = os.environ['T_TRADE_LOG_LEVEL']
        
        if 'T_TRADE_LOG_FILE' in os.environ:
            self.logging.file_path = os.environ['T_TRADE_LOG_FILE']
        
        # GUI配置
        if 'T_TRADE_THEME' in os.environ:
            self.gui.theme = os.environ['T_TRADE_THEME']
    
    def _notify_watchers(self):
        """通知配置变更监听器"""
        for callback in self._watchers:
            try:
                callback(self)
            except Exception as e:
                logger.error(f"Error in config watcher callback: {e}")


# 全局配置实例
_global_config = None


def get_config() -> UnifiedConfig:
    """获取全局配置实例"""
    global _global_config
    if _global_config is None:
        _global_config = UnifiedConfig()
    return _global_config


def reload_config():
    """重新加载全局配置"""
    global _global_config
    if _global_config:
        _global_config.load_config()


# 向后兼容的常量定义
config = get_config()
DATA_BASE_DIR = config.get_database_path("")
