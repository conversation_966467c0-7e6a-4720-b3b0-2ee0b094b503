"""
简化的数据适配器

专注于从data_engine获取已清洗的数据，移除了复杂的数据处理逻辑。
数据清洗已经前置到data_engine阶段，这里只负责：
1. 数据获取
2. 缓存管理  
3. 统一接口
4. 基础验证
"""

import pandas as pd
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class DataAdapter:
    """简化的数据适配器，专注于获取已清洗的数据"""
    
    def __init__(self, db_manage, data_engine=None, cache_enabled: bool = True, cache_size: int = 100):
        """
        初始化简化的数据适配器
        
        Parameters:
        -----------
        db_manage : DBManage
            数据库管理对象
        data_engine : DataEngine, optional
            数据引擎对象，如果不提供则从db_manage获取
        cache_enabled : bool, optional
            是否启用缓存，默认True
        cache_size : int, optional
            缓存大小，默认100
        """
        self.db_manage = db_manage
        
        # 获取或创建data_engine实例
        if data_engine is not None:
            self.data_engine = data_engine
        else:
            # 从data_engine模块导入
            from engine.data_engine.data_engine import DataEngine
            self.data_engine = DataEngine(quant_db=db_manage.quant_db if db_manage else None)
        
        self.cache_enabled = cache_enabled
        self.cache_size = cache_size
        self._cache = {}
        self._cache_stats = {'hits': 0, 'misses': 0}
        
        logger.info(f"DataAdapter initialized with cache_enabled={cache_enabled}")
    
    def get_price_data(self, assets: List[str], start_date: str, end_date: str, 
                      fields: List[str] = None) -> pd.DataFrame:
        """
        获取价格数据（已清洗，因子分析就绪）
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段，默认['close']
            
        Returns:
        --------
        pd.DataFrame
            已清洗的价格数据，因子分析就绪格式
        """
        try:
            if fields is None:
                fields = ['close']

            # 1. 检查缓存
            cache_key = self._generate_cache_key('price', fields, start_date, end_date, assets)

            if self.cache_enabled and cache_key in self._cache:
                self._cache_stats['hits'] += 1
                logger.debug(f"Cache hit for price data: {len(assets)} assets")
                return self._cache[cache_key].copy()

            self._cache_stats['misses'] += 1

            # 2. 从data_engine获取因子分析就绪的价格数据
            logger.debug(f"Fetching factor-ready price data for {len(assets)} assets from {start_date} to {end_date}")

            data = self.data_engine.get_factor_ready_price_data(
                assets=assets,
                start_date=start_date,
                end_date=end_date
            )

            if data.empty:
                logger.warning(f"No price data found for assets: {assets}")
                return pd.DataFrame()
            
            # 3. 提取需要的字段
            if len(fields) == 1 and fields[0] in data.columns:
                # 如果只需要一个字段，返回该字段的DataFrame格式
                result = data[[fields[0]]]
            else:
                # 多个字段或字段不存在时，返回所有可用字段
                available_fields = [f for f in fields if f in data.columns]
                if available_fields:
                    result = data[available_fields]
                else:
                    # 如果没有匹配的字段，返回第一列
                    result = data.iloc[:, [0]]
            
            # 4. 缓存结果
            if self.cache_enabled:
                self._update_cache(cache_key, result)
            
            logger.debug(f"Retrieved factor-ready price data: {result.shape}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting price data: {e}")
            raise
    
    def get_factor_data(self, factor_name: str, assets: List[str], 
                       start_date: str, end_date: str) -> pd.Series:
        """
        获取因子数据（已清洗）
        
        Parameters:
        -----------
        factor_name : str
            因子名称
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns:
        --------
        pd.Series
            已清洗的因子数据，MultiIndex格式
        """
        try:
            # 1. 检查缓存
            cache_key = self._generate_cache_key('factor', [factor_name], start_date, end_date, assets)
            
            if self.cache_enabled and cache_key in self._cache:
                self._cache_stats['hits'] += 1
                logger.debug(f"Cache hit for factor data: {factor_name}")
                return self._cache[cache_key].copy()
            
            self._cache_stats['misses'] += 1
            
            # 2. 从数据库获取因子数据（这里需要根据实际实现调整）
            logger.debug(f"Fetching factor data: {factor_name} for {len(assets)} assets")
            
            data = self._get_factor_from_db(
                factor_name=factor_name,
                assets=assets,
                start_date=start_date,
                end_date=end_date
            )
            
            if data.empty:
                logger.warning(f"No factor data found: {factor_name}")
                return pd.Series(dtype=float)
            
            # 3. 使用data_engine进行因子分析专用清洗
            if isinstance(data, pd.Series):
                cleaned_data = self.data_engine.prepare_factor_data_for_analysis(data)
            else:
                # 如果是DataFrame，提取因子列
                if factor_name in data.columns:
                    factor_series = data[factor_name]
                else:
                    factor_series = data.iloc[:, 0]  # 使用第一列
                cleaned_data = self.data_engine.prepare_factor_data_for_analysis(factor_series)
            
            # 4. 缓存结果
            if self.cache_enabled:
                self._update_cache(cache_key, cleaned_data)
            
            logger.debug(f"Retrieved and cleaned factor data: {cleaned_data.shape}")
            return cleaned_data
            
        except Exception as e:
            logger.error(f"Error getting factor data: {e}")
            raise
    
    def _generate_cache_key(self, data_type: str, fields: List[str], start_date: str, 
                           end_date: str, assets: List[str]) -> str:
        """生成缓存键"""
        assets_str = '_'.join(sorted(assets))
        fields_str = '_'.join(sorted(fields))
        return f"{data_type}_{fields_str}_{start_date}_{end_date}_{hash(assets_str)}"
    
    def _update_cache(self, cache_key: str, data: pd.DataFrame):
        """更新缓存"""
        if len(self._cache) >= self.cache_size:
            # 删除最旧的缓存项
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
        
        self._cache[cache_key] = data.copy()
    
    def _get_factor_from_db(self, factor_name: str, assets: List[str],
                           start_date: str, end_date: str) -> pd.Series:
        """从数据库获取因子数据（示例实现）"""
        try:
            # 这里需要根据实际的因子数据存储实现
            # 示例实现：
            
            if hasattr(self.db_manage, 'get_factor_data'):
                factor_data = self.db_manage.get_factor_data(
                    factor_name=factor_name,
                    assets=assets,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if isinstance(factor_data, pd.DataFrame) and factor_name in factor_data.columns:
                    return factor_data[factor_name]
                elif isinstance(factor_data, pd.Series):
                    return factor_data
            
            # 如果没有找到数据，返回空Series
            logger.warning(f"No factor data implementation found for {factor_name}")
            return pd.Series(dtype=float)
            
        except Exception as e:
            logger.error(f"Error fetching factor data from database: {e}")
            return pd.Series(dtype=float)
    
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        return self._cache_stats.copy()
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self._cache_stats = {'hits': 0, 'misses': 0}
        logger.info("Cache cleared")
    
    def get_data_engine_stats(self) -> Dict:
        """获取数据引擎的清洗统计信息"""
        return self.data_engine.get_cleaning_stats()

# 移除get_hs300_stocks方法 - 应该直接调用data_engine，避免不必要的转发层

    def get_data_strategy_info(self, assets: List[str], start_date: str, end_date: str) -> str:
        """
        获取数据策略信息

        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期

        Returns:
        --------
        str
            数据策略信息描述
        """
        try:
            if hasattr(self.data_engine, 'data_management_service') and self.data_engine.data_management_service:
                return self.data_engine.data_management_service.get_data_strategy_info(
                    assets, start_date, end_date
                )
            else:
                return f"数据策略信息\n总股票数: {len(assets)}\n时间范围: {start_date} 到 {end_date}"
        except Exception as e:
            logger.error(f"Error getting data strategy info: {e}")
            return f"数据策略信息获取失败: {str(e)}"
