"""
配置管理工具

提供因子研究框架的配置管理功能。
"""

from typing import Dict, Any, Optional
import yaml
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class FactorConfig:
    """
    因子研究配置管理器
    
    支持YAML和JSON格式的配置文件，提供配置验证和默认值管理。
    """
    
    DEFAULT_CONFIG = {
        'data_source': {
            'cache_enabled': True,
            'cache_size': 1000,
            'cache_ttl': 3600,  # 缓存过期时间（秒）
            'default_freq': 'D',  # 默认数据频率
            'price_adjust': 'qfq'  # 价格复权方式
        },
        'factor_calculation': {
            'parallel_enabled': False,
            'max_workers': 4,
            'default_lookback': 252,  # 默认回看期
            'min_periods': 20,  # 最小有效期数
            'handle_missing': 'drop',  # 缺失值处理方式
            'outlier_method': 'mad',  # 异常值处理方法
            'outlier_threshold': 3.0  # 异常值阈值
        },
        'alphalens_settings': {
            'periods': [1, 5, 10, 20],  # 持有期
            'quantiles': 5,  # 分位数数量
            'max_loss': 0.35,  # 最大数据丢失比例
            'zero_aware': False,  # 是否考虑零值
            'bins': None,  # 自定义分箱
            'groupby_labels': None  # 分组标签
        },
        'visualization': {
            'figure_size': [12, 8],  # 图表大小
            'dpi': 300,  # 分辨率
            'style': 'seaborn',  # 样式
            'save_format': 'png',  # 保存格式
            'color_palette': 'Set1',  # 颜色方案
            'font_size': 12,  # 字体大小
            'chinese_font': 'SimHei'  # 中文字体
        },
        'performance': {
            'enable_profiling': False,  # 是否启用性能分析
            'log_level': 'INFO',  # 日志级别
            'memory_limit': '8GB',  # 内存限制
            'timeout': 300  # 超时时间（秒）
        },
        'output': {
            'base_path': 'output',  # 输出基础路径
            'reports_path': 'reports',  # 报告路径
            'charts_path': 'charts',  # 图表路径
            'data_path': 'data',  # 数据路径
            'auto_create_dirs': True  # 自动创建目录
        }
    }
    
    def __init__(self, config_path: Optional[str] = None, **kwargs):
        """
        初始化配置管理器
        
        Parameters:
        -----------
        config_path : str, optional
            配置文件路径
        **kwargs : dict
            额外的配置参数
        """
        self.config = self.DEFAULT_CONFIG.copy()
        
        # 从文件加载配置
        if config_path:
            self.load_from_file(config_path)
            
        # 应用额外参数
        if kwargs:
            self.update_config(kwargs)
            
        # 验证配置
        self.validate_config()
        
    def load_from_file(self, config_path: str) -> None:
        """
        从文件加载配置
        
        Parameters:
        -----------
        config_path : str
            配置文件路径
        """
        try:
            config_path = Path(config_path)
            
            if not config_path.exists():
                logger.warning(f"Config file not found: {config_path}")
                return
                
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                    file_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    logger.error(f"Unsupported config file format: {config_path.suffix}")
                    return
                    
            # 递归合并配置
            self.config = self._merge_config(self.config, file_config)
            logger.info(f"Loaded config from: {config_path}")
            
        except Exception as e:
            logger.error(f"Error loading config file {config_path}: {str(e)}")
            
    def save_to_file(self, config_path: str, format: str = 'yaml') -> None:
        """
        保存配置到文件
        
        Parameters:
        -----------
        config_path : str
            配置文件路径
        format : str, optional
            文件格式，'yaml' 或 'json'
        """
        try:
            config_path = Path(config_path)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                if format.lower() == 'yaml':
                    yaml.dump(self.config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif format.lower() == 'json':
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"Unsupported format: {format}")
                    
            logger.info(f"Config saved to: {config_path}")
            
        except Exception as e:
            logger.error(f"Error saving config file {config_path}: {str(e)}")
            
    def update_config(self, updates: Dict[str, Any]) -> None:
        """
        更新配置
        
        Parameters:
        -----------
        updates : Dict[str, Any]
            更新的配置项
        """
        self.config = self._merge_config(self.config, updates)
        self.validate_config()
        
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Parameters:
        -----------
        key : str
            配置键，支持点分隔的嵌套键，如 'data_source.cache_enabled'
        default : Any, optional
            默认值
            
        Returns:
        --------
        Any
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Parameters:
        -----------
        key : str
            配置键，支持点分隔的嵌套键
        value : Any
            配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # 设置值
        config[keys[-1]] = value
        
    def validate_config(self) -> None:
        """验证配置的有效性"""
        try:
            # 验证数据源配置
            data_config = self.config.get('data_source', {})
            if not isinstance(data_config.get('cache_size', 1000), int):
                raise ValueError("data_source.cache_size must be an integer")
                
            # 验证因子计算配置
            factor_config = self.config.get('factor_calculation', {})
            if not isinstance(factor_config.get('max_workers', 4), int):
                raise ValueError("factor_calculation.max_workers must be an integer")
                
            # 验证alphalens配置
            alphalens_config = self.config.get('alphalens_settings', {})
            periods = alphalens_config.get('periods', [1, 5, 10, 20])
            if not isinstance(periods, list) or not all(isinstance(p, int) for p in periods):
                raise ValueError("alphalens_settings.periods must be a list of integers")
                
            # 验证可视化配置
            viz_config = self.config.get('visualization', {})
            figure_size = viz_config.get('figure_size', [12, 8])
            if not isinstance(figure_size, list) or len(figure_size) != 2:
                raise ValueError("visualization.figure_size must be a list of two numbers")
                
            logger.debug("Config validation passed")
            
        except Exception as e:
            logger.error(f"Config validation failed: {str(e)}")
            raise
            
    def _merge_config(self, base: Dict, update: Dict) -> Dict:
        """
        递归合并配置字典
        
        Parameters:
        -----------
        base : Dict
            基础配置
        update : Dict
            更新配置
            
        Returns:
        --------
        Dict
            合并后的配置
        """
        result = base.copy()
        
        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def to_dict(self) -> Dict[str, Any]:
        """
        返回配置字典
        
        Returns:
        --------
        Dict[str, Any]
            配置字典
        """
        return self.config.copy()
        
    def __getitem__(self, key: str) -> Any:
        """支持字典式访问"""
        return self.get(key)
        
    def __setitem__(self, key: str, value: Any) -> None:
        """支持字典式设置"""
        self.set(key, value)
        
    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return self.get(key) is not None
        
    def __repr__(self) -> str:
        """字符串表示"""
        return f"FactorConfig({self.config})"
