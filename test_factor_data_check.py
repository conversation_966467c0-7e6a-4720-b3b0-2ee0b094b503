#!/usr/bin/env python3
"""
测试因子分析数据检查和自动下载功能

这个脚本用于测试新增的数据检查和自动下载功能，
确保在因子分析前能够正确识别缺失数据并提供下载选项。
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_data_availability_check():
    """测试数据可用性检查功能"""
    try:
        # 导入必要的模块
        from database.db_manager import DBManager
        from factor_research.core.data_adapter import DataAdapter
        
        logger.info("=== 测试数据可用性检查功能 ===")
        
        # 初始化数据库管理器
        db_manage = DBManager()
        
        # 初始化数据适配器
        data_adapter = DataAdapter(db_manage=db_manage)
        
        # 测试股票列表
        test_assets = ['000001', '000002', '600000']
        start_date = '2023-01-01'
        end_date = '2023-12-31'
        
        logger.info(f"检查股票: {test_assets}")
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        # 检查数据可用性
        missing_assets = []
        
        if data_adapter and hasattr(data_adapter, 'data_engine'):
            data_engine = data_adapter.data_engine
            
            for asset in test_assets:
                try:
                    # 尝试获取价格数据
                    price_data = data_engine._get_raw_price_data_from_db([asset], start_date, end_date)
                    
                    if price_data.empty:
                        missing_assets.append(asset)
                        logger.warning(f"❌ {asset}: 无价格数据")
                    else:
                        data_count = len(price_data)
                        if data_count < 10:
                            missing_assets.append(asset)
                            logger.warning(f"⚠️  {asset}: 数据不足，仅有{data_count}条记录")
                        else:
                            logger.info(f"✅ {asset}: 数据完整，共{data_count}条记录")
                            
                except Exception as e:
                    logger.error(f"❌ {asset}: 检查失败 - {e}")
                    missing_assets.append(asset)
        
        # 输出检查结果
        logger.info(f"\n=== 数据检查结果 ===")
        logger.info(f"总股票数: {len(test_assets)}")
        logger.info(f"数据完整: {len(test_assets) - len(missing_assets)}")
        logger.info(f"需要下载: {len(missing_assets)}")
        
        if missing_assets:
            logger.warning(f"缺失数据的股票: {missing_assets}")
            return missing_assets
        else:
            logger.info("所有股票数据完整！")
            return []
            
    except Exception as e:
        logger.error(f"测试数据可用性检查失败: {e}")
        return None

def test_data_download_simulation():
    """模拟数据下载过程"""
    try:
        logger.info("\n=== 模拟数据下载过程 ===")
        
        # 导入必要的模块
        from database.db_manager import DBManager
        from engine.data_engine.data_engine import DataEngine
        
        # 初始化组件
        db_manage = DBManager()
        data_engine = DataEngine()
        
        # 测试下载单个股票数据
        test_symbol = '000001'
        start_date = '2023-01-01'
        end_date = '2023-01-31'  # 只下载一个月的数据进行测试
        
        logger.info(f"测试下载股票: {test_symbol}")
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        # 模拟下载过程
        logger.info("开始下载数据...")
        
        try:
            # 下载并清洗数据
            cleaned_data = data_engine.download_and_clean_data(
                symbol=test_symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if not cleaned_data.empty:
                logger.info(f"✅ 下载成功: {len(cleaned_data)}条记录")
                logger.info(f"数据列: {list(cleaned_data.columns)}")
                logger.info(f"日期范围: {cleaned_data.index.min()} 到 {cleaned_data.index.max()}")
                
                # 模拟保存到数据库
                logger.info("模拟保存到数据库...")
                # db_manage.save_stock_data(cleaned_data, test_symbol)
                logger.info("✅ 保存成功（模拟）")
                
                return True
            else:
                logger.warning("❌ 下载失败: 无数据返回")
                return False
                
        except Exception as e:
            logger.error(f"❌ 下载过程出错: {e}")
            return False
            
    except Exception as e:
        logger.error(f"模拟数据下载失败: {e}")
        return False

def test_integration():
    """集成测试：完整的数据检查和下载流程"""
    try:
        logger.info("\n=== 集成测试：完整流程 ===")
        
        # 1. 检查数据可用性
        missing_assets = test_data_availability_check()
        
        if missing_assets is None:
            logger.error("数据可用性检查失败")
            return False
        
        # 2. 如果有缺失数据，模拟下载过程
        if missing_assets:
            logger.info(f"\n发现{len(missing_assets)}只股票需要下载数据")
            
            # 模拟用户选择下载
            user_choice = True  # 模拟用户选择"是"
            
            if user_choice:
                logger.info("用户选择下载缺失数据...")
                
                # 模拟下载过程
                download_success = test_data_download_simulation()
                
                if download_success:
                    logger.info("✅ 数据下载完成，可以开始因子计算")
                    return True
                else:
                    logger.warning("⚠️  数据下载失败，将使用现有数据进行计算")
                    return False
            else:
                logger.info("用户选择不下载，将使用现有数据进行计算")
                return True
        else:
            logger.info("✅ 数据完整，可以直接开始因子计算")
            return True
            
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试因子分析数据检查和自动下载功能")
    logger.info("=" * 60)
    
    try:
        # 运行集成测试
        success = test_integration()
        
        logger.info("\n" + "=" * 60)
        if success:
            logger.info("✅ 测试完成：功能正常工作")
        else:
            logger.warning("⚠️  测试完成：发现问题，需要检查")
            
    except Exception as e:
        logger.error(f"测试过程出错: {e}")
        logger.info("\n" + "=" * 60)
        logger.error("❌ 测试失败")

if __name__ == "__main__":
    main()
