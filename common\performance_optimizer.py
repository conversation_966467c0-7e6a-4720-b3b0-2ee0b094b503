"""
性能优化工具

提供系统性能监控、分析和优化功能。
包括内存管理、缓存优化、并发处理等。
"""

import time
import psutil
import threading
import functools
from typing import Dict, Any, Callable, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import gc

from .unified_logger import get_logger

logger = get_logger()


class PerformanceMonitor:
    """
    性能监控器
    
    监控系统资源使用情况和性能指标
    """
    
    def __init__(self, sample_interval: float = 1.0):
        """
        初始化性能监控器
        
        Parameters:
        -----------
        sample_interval : float
            采样间隔（秒）
        """
        self.sample_interval = sample_interval
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 性能数据
        self.metrics = {
            'cpu_percent': deque(maxlen=100),
            'memory_percent': deque(maxlen=100),
            'memory_used_mb': deque(maxlen=100),
            'disk_io_read': deque(maxlen=100),
            'disk_io_write': deque(maxlen=100),
            'network_sent': deque(maxlen=100),
            'network_recv': deque(maxlen=100),
            'timestamps': deque(maxlen=100)
        }
        
        # 统计信息
        self.stats = {
            'peak_cpu': 0.0,
            'peak_memory': 0.0,
            'avg_cpu': 0.0,
            'avg_memory': 0.0,
            'start_time': None
        }
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.stats['start_time'] = datetime.now()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        logger.info("Performance monitoring stopped")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 获取系统指标
                cpu_percent = psutil.cpu_percent()
                memory = psutil.virtual_memory()
                disk_io = psutil.disk_io_counters()
                network_io = psutil.net_io_counters()
                
                # 记录数据
                timestamp = datetime.now()
                self.metrics['cpu_percent'].append(cpu_percent)
                self.metrics['memory_percent'].append(memory.percent)
                self.metrics['memory_used_mb'].append(memory.used / 1024 / 1024)
                self.metrics['disk_io_read'].append(disk_io.read_bytes if disk_io else 0)
                self.metrics['disk_io_write'].append(disk_io.write_bytes if disk_io else 0)
                self.metrics['network_sent'].append(network_io.bytes_sent if network_io else 0)
                self.metrics['network_recv'].append(network_io.bytes_recv if network_io else 0)
                self.metrics['timestamps'].append(timestamp)
                
                # 更新统计信息
                self.stats['peak_cpu'] = max(self.stats['peak_cpu'], cpu_percent)
                self.stats['peak_memory'] = max(self.stats['peak_memory'], memory.percent)
                
                if len(self.metrics['cpu_percent']) > 0:
                    self.stats['avg_cpu'] = sum(self.metrics['cpu_percent']) / len(self.metrics['cpu_percent'])
                    self.stats['avg_memory'] = sum(self.metrics['memory_percent']) / len(self.metrics['memory_percent'])
                
                time.sleep(self.sample_interval)
                
            except Exception as e:
                logger.error(f"Error in performance monitoring: {e}")
                time.sleep(self.sample_interval)
    
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前统计信息"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            return {
                'current_cpu': cpu_percent,
                'current_memory': memory.percent,
                'current_memory_mb': memory.used / 1024 / 1024,
                'peak_cpu': self.stats['peak_cpu'],
                'peak_memory': self.stats['peak_memory'],
                'avg_cpu': self.stats['avg_cpu'],
                'avg_memory': self.stats['avg_memory'],
                'monitoring_duration': (datetime.now() - self.stats['start_time']).total_seconds() if self.stats['start_time'] else 0
            }
        except Exception as e:
            logger.error(f"Error getting current stats: {e}")
            return {}
    
    def get_metrics_history(self) -> Dict[str, list]:
        """获取指标历史数据"""
        return {key: list(values) for key, values in self.metrics.items()}


class FunctionProfiler:
    """
    函数性能分析器
    
    分析函数执行时间和调用频率
    """
    
    def __init__(self):
        self.profiles = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'last_call': None
        })
        self.lock = threading.Lock()
    
    def profile(self, func_name: str = None):
        """性能分析装饰器"""
        def decorator(func):
            name = func_name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    duration = end_time - start_time
                    self._record_call(name, duration)
            return wrapper
        return decorator
    
    def _record_call(self, func_name: str, duration: float):
        """记录函数调用"""
        with self.lock:
            profile = self.profiles[func_name]
            profile['call_count'] += 1
            profile['total_time'] += duration
            profile['avg_time'] = profile['total_time'] / profile['call_count']
            profile['min_time'] = min(profile['min_time'], duration)
            profile['max_time'] = max(profile['max_time'], duration)
            profile['last_call'] = datetime.now()
    
    def get_profiles(self) -> Dict[str, Dict[str, Any]]:
        """获取所有性能分析结果"""
        with self.lock:
            return dict(self.profiles)
    
    def get_top_functions(self, by: str = 'total_time', limit: int = 10) -> list:
        """获取性能排名前N的函数"""
        profiles = self.get_profiles()
        sorted_profiles = sorted(
            profiles.items(),
            key=lambda x: x[1][by],
            reverse=True
        )
        return sorted_profiles[:limit]
    
    def reset_profiles(self):
        """重置性能分析数据"""
        with self.lock:
            self.profiles.clear()
        logger.info("Function profiles reset")


class CacheManager:
    """
    缓存管理器
    
    提供智能缓存管理和优化
    """
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存管理器
        
        Parameters:
        -----------
        max_size : int
            最大缓存条目数
        ttl : int
            缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
        self.creation_times = {}
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size': 0
        }
    
    def get(self, key: str, default=None):
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                # 检查是否过期
                if self._is_expired(key):
                    self._remove(key)
                    self.stats['misses'] += 1
                    return default
                
                # 更新访问时间
                self.access_times[key] = time.time()
                self.stats['hits'] += 1
                return self.cache[key]
            else:
                self.stats['misses'] += 1
                return default
    
    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        with self.lock:
            # 检查是否需要清理空间
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            # 设置缓存
            self.cache[key] = value
            current_time = time.time()
            self.access_times[key] = current_time
            self.creation_times[key] = current_time
            
            # 设置自定义TTL
            if ttl is not None:
                self.creation_times[key] = current_time - (self.ttl - ttl)
            
            self.stats['size'] = len(self.cache)
    
    def delete(self, key: str):
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                self._remove(key)
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.creation_times.clear()
            self.stats['size'] = 0
        logger.info("Cache cleared")
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self.creation_times:
            return True
        return time.time() - self.creation_times[key] > self.ttl
    
    def _remove(self, key: str):
        """移除缓存项"""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
        self.creation_times.pop(key, None)
        self.stats['size'] = len(self.cache)
    
    def _evict_lru(self):
        """移除最近最少使用的缓存项"""
        if not self.access_times:
            return
        
        # 找到最久未访问的key
        lru_key = min(self.access_times.items(), key=lambda x: x[1])[0]
        self._remove(lru_key)
        self.stats['evictions'] += 1
    
    def cleanup_expired(self):
        """清理过期缓存"""
        with self.lock:
            expired_keys = [
                key for key in self.cache.keys()
                if self._is_expired(key)
            ]
            
            for key in expired_keys:
                self._remove(key)
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'total_requests': total_requests
            }


class MemoryManager:
    """
    内存优化器
    
    提供内存使用优化和垃圾回收管理
    """
    
    @staticmethod
    def force_gc():
        """强制垃圾回收"""
        collected = gc.collect()
        logger.debug(f"Garbage collection completed, collected {collected} objects")
        return collected
    
    @staticmethod
    def get_memory_usage() -> Dict[str, Any]:
        """获取内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,
                'vms_mb': memory_info.vms / 1024 / 1024,
                'percent': memory_percent,
                'available_mb': psutil.virtual_memory().available / 1024 / 1024
            }
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {}
    
    @staticmethod
    def optimize_memory():
        """优化内存使用"""
        try:
            # 强制垃圾回收
            collected = MemoryOptimizer.force_gc()
            
            # 获取优化后的内存使用情况
            memory_after = MemoryOptimizer.get_memory_usage()
            
            logger.info(f"Memory optimization completed: collected {collected} objects, "
                       f"current usage: {memory_after.get('rss_mb', 0):.1f}MB")
            
            return {
                'collected_objects': collected,
                'memory_usage': memory_after
            }
        except Exception as e:
            logger.error(f"Error optimizing memory: {e}")
            return {}


class PerformanceManager:
    """
    性能优化器主类
    
    整合所有性能优化功能
    """
    
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.profiler = FunctionProfiler()
        self.cache_manager = CacheManager()
        self.memory_manager = MemoryManager()
        
        # 自动清理定时器
        self._cleanup_timer = None
        self._start_auto_cleanup()
    
    def start_monitoring(self):
        """开始性能监控"""
        self.monitor.start_monitoring()
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitor.stop_monitoring()
    
    def profile_function(self, func_name: str = None):
        """函数性能分析装饰器"""
        return self.profiler.profile(func_name)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            'system_stats': self.monitor.get_current_stats(),
            'function_profiles': self.profiler.get_top_functions(),
            'cache_stats': self.cache_manager.get_stats(),
            'memory_usage': self.memory_manager.get_memory_usage()
        }
    
    def optimize_system(self):
        """系统优化"""
        logger.info("Starting system optimization...")
        
        # 清理过期缓存
        self.cache_manager.cleanup_expired()
        
        # 内存优化
        memory_result = self.memory_manager.optimize_memory()
        
        # 获取优化后的状态
        report = self.get_performance_report()
        
        logger.info("System optimization completed")
        return {
            'memory_optimization': memory_result,
            'performance_report': report
        }
    
    def _start_auto_cleanup(self):
        """启动自动清理"""
        def cleanup_loop():
            while True:
                try:
                    time.sleep(300)  # 每5分钟清理一次
                    self.cache_manager.cleanup_expired()
                    
                    # 每小时进行一次内存优化
                    if int(time.time()) % 3600 < 300:
                        self.memory_manager.optimize_memory()
                        
                except Exception as e:
                    logger.error(f"Error in auto cleanup: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()


# 全局性能管理器实例
_global_manager = None


def get_performance_manager() -> PerformanceManager:
    """获取全局性能管理器"""
    global _global_manager
    if _global_manager is None:
        _global_manager = PerformanceManager()
    return _global_manager


# 便捷装饰器
def profile(func_name: str = None):
    """性能分析装饰器"""
    return get_performance_manager().profile_function(func_name)


def manage_performance():
    """执行性能管理"""
    return get_performance_manager().optimize_system()
