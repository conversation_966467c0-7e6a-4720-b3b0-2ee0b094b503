from .quant_db import QuantDB
from .hist_db import HistDB
from .realtime_db import RealTimeDB
from .snapshot_db import SnapshotDB
from engine.data_engine.data_engine import DataEngine
import os
import threading
import time
import datetime
import pandas as pd

# 导入优化工具
try:
    from database.advanced_optimizer import AdvancedDatabaseOptimizer
    ADVANCED_OPTIMIZER_AVAILABLE = True
except ImportError:
    ADVANCED_OPTIMIZER_AVAILABLE = False

class DBManage(object):
    def __init__(self, account_manage):
        self.account_manage = account_manage
        self.data_engine = DataEngine()
        self.quant_db = QuantDB()
        self.hist_db = HistDB(self.data_engine)
        self.realtime_db = RealTimeDB(self.data_engine)
        self.snapshot_db = SnapshotDB()

        # 初始化高级优化器
        if ADVANCED_OPTIMIZER_AVAILABLE:
            self.advanced_optimizer = AdvancedDatabaseOptimizer(self)
        else:
            self.advanced_optimizer = None

    def init_database(self):
        """
        数据库初始化：没有数据库时创建数据库文件和基础表结构
        """
        # 确保数据库文件存在
        if not os.path.exists(self.quant_db.db_path):
            # 创建空数据库文件并初始化表
            self.quant_db.conn.close()  # 触发文件创建
        # 初始化实时数据表
        self.realtime_db.init_realtime_tables()

    def update_hist_data_background(self):
        """
        后台线程：根据所有账户股票池，更新或下载股票历史数据
        """
        def update_task():
            accounts = self.account_manage._list_account()
            for account_name in accounts:
                info = self.account_manage._load_account(account_name)
                if not info:
                    continue
                equity_pool = info.get("equity_pool", [])
                adjust = info.get("adjust", "前复权")
                self.hist_db.update_hist_data(equity_pool, adjust=adjust)
        threading.Thread(target=update_task, daemon=True).start()

    def update_realtime_once(self):
        """
        单次刷新实时数据并更新到数据库中，打印耗时用于调试。
        如果实时数据库没有数据，则自动执行一次刷新。
        支持多标的/多表实时数据库，逐个判断并刷新。
        """
        # 检查所有实时数据库表（如有多个标的/表）是否有数据
        need_refresh = False
        try:
            # 假设有方法获取所有实时表名
            table_names = self.realtime_db.get_all_realtime_tables() if hasattr(self.realtime_db, "get_all_realtime_tables") else []
            if not table_names:
                # 如果没有表名方法，默认只检查主表
                table_names = [None]
            for table in table_names:
                df = self.realtime_db.query_realtime(table=table) if table else self.realtime_db.query_realtime()
                if df is None or df.empty:
                    print(f"[DBManage] 实时数据库表 {table or 'default'} 无数据，自动刷新。")
                    need_refresh = True
                    try:
                        self.realtime_db.refresh_realtime() if table else self.realtime_db.refresh_realtime(self.data_engine)
                    except Exception as e:
                        print(f"[DBManage] 首次实时数据刷新异常: {e}")
        except Exception as e:
            print(f"[DBManage] 检查实时数据库异常: {e}")
            need_refresh = True
            try:
                self.realtime_db.refresh_realtime(self.data_engine)
            except Exception as e2:
                print(f"[DBManage] 首次实时数据刷新异常: {e2}")

    def update_realtime_data_periodic(self, interval_sec=60, end_time="15:00", window_min=30):
        """
        周期性刷新实时数据并更新到数据库中，仅在尾盘前window_min分钟内每interval_sec秒刷新一次
        :param interval_sec: 刷新周期（秒）
        :param end_time: 收盘时间（如"15:00"）
        :param window_min: 距离收盘的分钟数，窗口内才刷新
        """
        def refresh_task():
            while True:
                now = datetime.datetime.now()
                today_end = now.replace(hour=int(end_time.split(":")[0]), minute=int(end_time.split(":")[1]), second=0, microsecond=0)
                window_start = today_end - datetime.timedelta(minutes=window_min)
                if window_start <= now < today_end:
                    try:
                        self.realtime_db.refresh_realtime()
                    except Exception as e:
                        print(f"[DBManage] 实时数据刷新异常: {e}")
                # 其余时间不刷新
                time.sleep(interval_sec)
        threading.Thread(target=refresh_task, daemon=True).start()

    def main_loop(self):
        """
        启动数据库相关的后台任务
        """
        # 初始化数据库
        self.init_database()

        # 下载和更新沪深300核心数据
        self.download_hs300_core_data()

        # 启动后台历史数据更新
        self.update_hist_data_background()

        # 单次刷新实时数据
        self.update_realtime_once()
        # 启动尾盘前30分钟每分钟刷新一次实时数据
        self.update_realtime_data_periodic(interval_sec=60, end_time="15:00", window_min=30)

    def download_hs300_core_data(self):
        """
        下载和更新沪深300核心数据
        """
        import threading

        def download_task():
            try:
                print("[DBManage] 开始检查和更新沪深300核心数据...")

                # 获取沪深300股票列表
                hs300_stocks = self._get_hs300_stock_list()
                print(f"[DBManage] 沪深300股票池: {len(hs300_stocks)}只")

                # 1. 检查和更新历史数据
                print("[DBManage] 检查历史数据完整性...")
                missing_history_stocks = self._check_missing_history_data(hs300_stocks)

                if missing_history_stocks:
                    print(f"[DBManage] 需要下载历史数据的股票: {len(missing_history_stocks)}只")
                    self._download_missing_history_data(missing_history_stocks)
                else:
                    print("[DBManage] 历史数据已完整，无需下载")

                # 2. 批量更新实时数据（利用现有机制）
                print("[DBManage] 更新实时数据...")
                self.update_realtime_once()
                print("[DBManage] 实时数据更新完成")

                print("[DBManage] 沪深300核心数据检查和更新完成")

            except Exception as e:
                print(f"[DBManage] 沪深300核心数据更新异常: {e}")
                import traceback
                traceback.print_exc()

        # 在后台线程中执行下载任务
        threading.Thread(target=download_task, daemon=True).start()

    def _get_hs300_stock_list(self):
        """
        获取沪深300股票列表
        """
        # 沪深300核心成分股列表（精选150只）
        hs300_stocks = [
            # 银行
            "000001", "000002", "600000", "600036", "600016", "000858", "002142", "600919", "601166", "601169",
            # 非银金融
            "601318", "601601", "000166", "002736", "600030", "601628", "000776", "600837", "002415", "000783",
            # 食品饮料
            "600519", "000858", "600887", "002304", "000596", "600809", "000568", "600779", "002568", "000799",
            # 医药生物
            "000661", "600276", "000963", "002007", "600085", "002422", "300015", "000538", "002821", "300347",
            # 电子
            "002415", "000725", "002230", "000063", "002241", "300059", "002456", "000100", "002049", "300433",
            # 计算机
            "000977", "002410", "300454", "002405", "300496", "002153", "300033", "002065", "300253", "000938",
            # 通信
            "000063", "600050", "000997", "002017", "600498", "000070", "002396", "600776", "000555", "002313",
            # 家用电器
            "000333", "000651", "002032", "000921", "002050", "600690", "000418", "002508", "000100", "600060",
            # 汽车
            "000625", "601633", "000800", "002594", "600104", "000550", "002460", "000559", "600066", "000957",
            # 房地产
            "000002", "001979", "600048", "000069", "600383", "000656", "600340", "000402", "001696", "600606",
            # 建筑材料
            "000877", "600585", "000401", "600801", "000789", "002271", "600309", "000786", "002233", "600720",
            # 钢铁
            "000709", "600019", "000898", "000932", "600022", "000825", "000959", "600126", "000717", "000761",
            # 有色金属
            "600362", "000831", "002460", "000060", "600111", "000630", "002466", "600549", "000807", "600219",
            # 化工
            "600309", "000792", "002648", "000830", "600426", "000301", "002601", "000059", "600160", "000408",
            # 机械设备
            "000157", "002415", "000425", "600031", "000680", "002202", "000528", "600765", "002008", "000338"
        ]

        # 去重并返回
        return list(set(hs300_stocks))

    def _download_stock_history(self, stock_code: str, start_date, end_date):
        """
        下载单只股票的历史数据
        """
        try:
            # 使用现有的数据引擎下载历史数据
            from engine.data_engine.data_engine import DataEngine

            if hasattr(self, 'data_engine'):
                data_engine = self.data_engine
            else:
                data_engine = DataEngine()

            # 下载日线数据
            hist_data = data_engine._get_stock_hist_info(stock_code, "daily", "")

            if not hist_data.empty:
                # 过滤日期范围
                hist_data['date'] = pd.to_datetime(hist_data['date'])
                mask = (hist_data['date'] >= start_date) & (hist_data['date'] <= end_date)
                filtered_data = hist_data[mask]

                if not filtered_data.empty:
                    # 存储到数据库
                    try:
                        # 准备数据格式
                        filtered_data = filtered_data.copy()
                        filtered_data['code'] = stock_code

                        # 确保日期格式正确
                        filtered_data['date'] = filtered_data['date'].dt.strftime('%Y-%m-%d')

                        # 选择需要的列（根据实际数据结构调整）
                        required_columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount']
                        available_columns = [col for col in required_columns if col in filtered_data.columns]

                        if 'close' in available_columns:
                            save_data = filtered_data[available_columns]

                            # 检查是否存在历史数据表，如果不存在则创建
                            self._ensure_history_table_exists()

                            # 删除该股票的旧数据（避免重复）
                            delete_sql = f"DELETE FROM stock_daily WHERE code = '{stock_code}'"
                            self.quant_db.conn.execute(delete_sql)

                            # 插入新数据
                            self.quant_db.insert_df('stock_daily', save_data)

                            print(f"[DBManage] 成功保存股票{stock_code}历史数据到数据库: {len(save_data)}条记录")
                        else:
                            print(f"[DBManage] 股票{stock_code}历史数据缺少必要字段，仅下载未保存")

                    except Exception as e:
                        print(f"[DBManage] 保存股票{stock_code}历史数据到数据库失败: {e}")
                        # 即使保存失败，也记录下载成功
                        print(f"[DBManage] 股票{stock_code}历史数据已下载但未保存: {len(filtered_data)}条记录")
                else:
                    print(f"[DBManage] 股票{stock_code}在指定日期范围内无历史数据")
            else:
                print(f"[DBManage] 股票{stock_code}历史数据下载失败")

        except Exception as e:
            print(f"[DBManage] 下载股票{stock_code}历史数据异常: {e}")

    def _download_stock_realtime(self, stock_code: str):
        """
        下载单只股票的实时数据
        """
        try:
            # 使用现有的数据引擎下载实时数据
            from engine.data_engine.data_engine import DataEngine

            if hasattr(self, 'data_engine'):
                data_engine = self.data_engine
            else:
                data_engine = DataEngine()

            # 获取实时数据 - 使用正确的方法名
            realtime_data = data_engine._get_stock_spot_info_by_code(stock_code)

            if realtime_data and realtime_data.get('price'):
                # 构造实时数据字典
                stock_data = {
                    'code': stock_code,
                    'name': realtime_data.get('name', ''),
                    'latest_price': realtime_data.get('price', 0),
                    'date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }

                # 存储到实时数据表
                self.realtime_db.update_stock_realtime(stock_code, stock_data)
                print(f"[DBManage] 成功更新股票{stock_code}实时数据: {realtime_data.get('price')}")
            else:
                print(f"[DBManage] 股票{stock_code}实时数据获取失败")

        except Exception as e:
            print(f"[DBManage] 下载股票{stock_code}实时数据异常: {e}")

    def _ensure_history_table_exists(self):
        """
        确保历史数据表存在，如果不存在则创建
        """
        try:
            # 检查表是否存在
            table_check = self.quant_db.conn.execute(
                "SELECT 1 FROM information_schema.tables WHERE table_name='stock_daily'"
            ).fetchall()

            if not table_check:
                # 创建日线历史数据表
                create_sql = """
                CREATE TABLE stock_daily (
                    date VARCHAR,
                    code VARCHAR,
                    open DOUBLE,
                    high DOUBLE,
                    low DOUBLE,
                    close DOUBLE,
                    volume DOUBLE,
                    amount DOUBLE,
                    PRIMARY KEY (date, code)
                )
                """
                self.quant_db.conn.execute(create_sql)
                print("[DBManage] 创建stock_daily表成功")

        except Exception as e:
            print(f"[DBManage] 确保历史数据表存在失败: {e}")

    def run_advanced_optimization(self):
        """运行高级数据库优化"""
        if not self.advanced_optimizer:
            print("[DBManage] 高级优化器不可用")
            return None

        try:
            print("[DBManage] 开始高级数据库优化...")
            results = self.advanced_optimizer.run_comprehensive_optimization()

            # 输出优化结果摘要
            total_indexes = sum(len(r.get('created', [])) for r in results['index_creations'].values())
            print(f"[DBManage] 高级优化完成，创建了{total_indexes}个索引")

            for recommendation in results.get('recommendations', []):
                print(f"[DBManage] 建议: {recommendation}")

            return results

        except Exception as e:
            print(f"[DBManage] 高级数据库优化失败: {e}")
            return None

    def analyze_query_performance(self, query: str, params=None):
        """分析查询性能"""
        if not self.advanced_optimizer:
            print("[DBManage] 高级优化器不可用")
            return None

        try:
            analysis = self.advanced_optimizer.query_analyzer.analyze_query(query, params)

            print(f"[DBManage] 查询分析结果:")
            print(f"  执行时间: {analysis.get('execution_time', 0):.3f}秒")
            print(f"  结果数量: {analysis.get('result_count', 0)}")
            print(f"  性能评级: {analysis.get('performance_rating', 'unknown')}")

            for suggestion in analysis.get('suggestions', []):
                print(f"  建议: {suggestion}")

            return analysis

        except Exception as e:
            print(f"[DBManage] 查询性能分析失败: {e}")
            return None

    def get_slow_queries_report(self):
        """获取慢查询报告"""
        if not self.advanced_optimizer:
            print("[DBManage] 高级优化器不可用")
            return None

        try:
            report = self.advanced_optimizer.query_analyzer.get_slow_queries_report()

            if 'message' in report:
                print(f"[DBManage] {report['message']}")
            else:
                print(f"[DBManage] 慢查询报告:")
                print(f"  总慢查询数: {report.get('total_slow_queries', 0)}")
                print(f"  最慢查询时间: {report.get('slowest_query_time', 0):.3f}秒")

                for issue in report.get('common_issues', []):
                    print(f"  常见问题: {issue}")

            return report

        except Exception as e:
            print(f"[DBManage] 慢查询报告生成失败: {e}")
            return None



    def _check_missing_history_data(self, stock_list):
        """
        检查缺失历史数据的股票

        Parameters:
        -----------
        stock_list : List[str]
            需要检查的股票列表

        Returns:
        --------
        List[str]
            缺失历史数据的股票代码列表
        """
        try:
            # 确保历史数据表存在
            self._ensure_history_table_exists()

            missing_stocks = []

            # 设置期望的最少记录数（约1年交易日）
            expected_min_records = 200  # 期望最少200条记录

            print(f"[DBManage] 检查历史数据完整性（期望最少{expected_min_records}条记录）...")

            for stock_code in stock_list:
                try:
                    # 检查该股票的历史数据记录数
                    count_result = self.quant_db.conn.execute(
                        f"SELECT COUNT(*) FROM stock_daily WHERE code = '{stock_code}'"
                    ).fetchone()

                    record_count = count_result[0] if count_result else 0

                    if record_count < expected_min_records:
                        missing_stocks.append(stock_code)
                        if record_count == 0:
                            print(f"  {stock_code}: 无历史数据")
                        else:
                            print(f"  {stock_code}: 仅有{record_count}条记录，需要补充")

                except Exception as e:
                    print(f"  {stock_code}: 检查失败 - {e}")
                    missing_stocks.append(stock_code)

            print(f"[DBManage] 历史数据检查完成: {len(missing_stocks)}/{len(stock_list)}只股票需要下载")
            return missing_stocks

        except Exception as e:
            print(f"[DBManage] 检查缺失历史数据异常: {e}")
            # 如果检查失败，返回所有股票（安全起见）
            return stock_list

    def _download_missing_history_data(self, missing_stocks):
        """
        下载缺失的历史数据

        Parameters:
        -----------
        missing_stocks : List[str]
            需要下载历史数据的股票代码列表
        """
        try:
            from datetime import datetime, timedelta

            if not missing_stocks:
                return

            # 计算下载日期范围（最近3年）
            end_date = datetime.now()
            start_date = end_date - timedelta(days=3*365)

            print(f"[DBManage] 开始下载{len(missing_stocks)}只股票的历史数据...")
            print(f"[DBManage] 日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

            # 分批下载，避免请求过于频繁
            batch_size = 5  # 每批5只股票
            total_stocks = len(missing_stocks)
            success_count = 0

            for i in range(0, total_stocks, batch_size):
                batch_stocks = missing_stocks[i:i+batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total_stocks + batch_size - 1) // batch_size

                print(f"[DBManage] 下载第{batch_num}/{total_batches}批: {batch_stocks}")

                for stock_code in batch_stocks:
                    try:
                        self._download_stock_history(stock_code, start_date, end_date)
                        success_count += 1
                    except Exception as e:
                        print(f"[DBManage] 下载股票{stock_code}历史数据失败: {e}")

                # 批次间暂停
                if i + batch_size < total_stocks:
                    import time
                    time.sleep(2)

            print(f"[DBManage] 历史数据下载完成: {success_count}/{total_stocks}只股票成功")

        except Exception as e:
            print(f"[DBManage] 下载缺失历史数据异常: {e}")
            import traceback
            traceback.print_exc()

