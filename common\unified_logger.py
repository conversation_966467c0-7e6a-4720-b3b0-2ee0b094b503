"""
统一日志管理系统

整合日志记录功能，提供统一的日志接口。
支持多种输出方式、日志级别和格式化选项。
"""

import os
import sys
import logging
import logging.handlers
from typing import Optional, Dict, Any
from pathlib import Path
from datetime import datetime
import threading

from .unified_config import get_config


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class UnifiedLogger:
    """
    统一日志管理器
    
    主要功能:
    - 多种输出方式（控制台、文件、网络等）
    - 日志轮转和压缩
    - 性能监控
    - 上下文信息
    - 异步日志记录
    """
    
    def __init__(self, name: str = "t_trade"):
        """
        初始化日志管理器
        
        Parameters:
        -----------
        name : str
            日志器名称
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.config = get_config().logging
        
        # 内部状态
        self._handlers = {}
        self._filters = {}
        self._context = {}
        self._stats = {
            'total_logs': 0,
            'error_count': 0,
            'warning_count': 0,
            'start_time': datetime.now()
        }
        self._lock = threading.Lock()
        
        # 初始化日志器
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        try:
            # 清除现有处理器
            self.logger.handlers.clear()
            
            # 设置日志级别
            level = getattr(logging, self.config.level.upper(), logging.INFO)
            self.logger.setLevel(level)
            
            # 添加控制台处理器
            if self.config.console_enabled:
                self._add_console_handler()
            
            # 添加文件处理器
            if self.config.file_enabled:
                self._add_file_handler()
            
            # 防止重复日志
            self.logger.propagate = False
            
            self.logger.info("Unified logger initialized successfully")
            
        except Exception as e:
            print(f"Error setting up logger: {e}")
    
    def _add_console_handler(self):
        """添加控制台处理器"""
        try:
            handler = logging.StreamHandler(sys.stdout)
            
            # 使用彩色格式化器
            formatter = ColoredFormatter(self.config.format)
            handler.setFormatter(formatter)
            
            self.logger.addHandler(handler)
            self._handlers['console'] = handler
            
        except Exception as e:
            print(f"Error adding console handler: {e}")
    
    def _add_file_handler(self):
        """添加文件处理器"""
        try:
            # 确保日志目录存在
            log_path = Path(self.config.file_path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 使用轮转文件处理器
            handler = logging.handlers.RotatingFileHandler(
                filename=str(log_path),
                maxBytes=self.config.max_file_size,
                backupCount=self.config.backup_count,
                encoding='utf-8'
            )
            
            # 使用标准格式化器
            formatter = logging.Formatter(self.config.format)
            handler.setFormatter(formatter)
            
            self.logger.addHandler(handler)
            self._handlers['file'] = handler
            
        except Exception as e:
            print(f"Error adding file handler: {e}")
    
    def set_level(self, level: str):
        """设置日志级别"""
        try:
            log_level = getattr(logging, level.upper(), logging.INFO)
            self.logger.setLevel(log_level)
            self.config.level = level.upper()
            self.info(f"Log level changed to: {level}")
        except Exception as e:
            self.error(f"Error setting log level: {e}")
    
    def add_context(self, **kwargs):
        """添加上下文信息"""
        with self._lock:
            self._context.update(kwargs)
    
    def remove_context(self, *keys):
        """移除上下文信息"""
        with self._lock:
            for key in keys:
                self._context.pop(key, None)
    
    def clear_context(self):
        """清除所有上下文信息"""
        with self._lock:
            self._context.clear()
    
    def _format_message(self, message: str) -> str:
        """格式化消息（添加上下文信息）"""
        if not self._context:
            return message
        
        context_str = " | ".join([f"{k}={v}" for k, v in self._context.items()])
        return f"[{context_str}] {message}"
    
    def _update_stats(self, level: str):
        """更新统计信息"""
        with self._lock:
            self._stats['total_logs'] += 1
            if level == 'ERROR':
                self._stats['error_count'] += 1
            elif level == 'WARNING':
                self._stats['warning_count'] += 1
    
    # ==================== 日志记录方法 ====================
    
    def debug(self, message: str, *args, **kwargs):
        """记录调试信息"""
        formatted_message = self._format_message(message)
        self.logger.debug(formatted_message, *args, **kwargs)
        self._update_stats('DEBUG')
    
    def info(self, message: str, *args, **kwargs):
        """记录信息"""
        formatted_message = self._format_message(message)
        self.logger.info(formatted_message, *args, **kwargs)
        self._update_stats('INFO')
    
    def warning(self, message: str, *args, **kwargs):
        """记录警告"""
        formatted_message = self._format_message(message)
        self.logger.warning(formatted_message, *args, **kwargs)
        self._update_stats('WARNING')
    
    def error(self, message: str, *args, **kwargs):
        """记录错误"""
        formatted_message = self._format_message(message)
        self.logger.error(formatted_message, *args, **kwargs)
        self._update_stats('ERROR')
    
    def critical(self, message: str, *args, **kwargs):
        """记录严重错误"""
        formatted_message = self._format_message(message)
        self.logger.critical(formatted_message, *args, **kwargs)
        self._update_stats('CRITICAL')
    
    def exception(self, message: str, *args, **kwargs):
        """记录异常（包含堆栈跟踪）"""
        formatted_message = self._format_message(message)
        self.logger.exception(formatted_message, *args, **kwargs)
        self._update_stats('ERROR')
    
    # ==================== 便捷方法 ====================
    
    def log_function_call(self, func_name: str, args: tuple = None, kwargs: dict = None):
        """记录函数调用"""
        args_str = str(args) if args else ""
        kwargs_str = str(kwargs) if kwargs else ""
        self.debug(f"Function called: {func_name}({args_str}, {kwargs_str})")
    
    def log_performance(self, operation: str, duration: float, **metrics):
        """记录性能信息"""
        metrics_str = " | ".join([f"{k}={v}" for k, v in metrics.items()])
        self.info(f"Performance: {operation} took {duration:.3f}s | {metrics_str}")
    
    def log_data_info(self, data_type: str, count: int, **details):
        """记录数据信息"""
        details_str = " | ".join([f"{k}={v}" for k, v in details.items()])
        self.info(f"Data: {data_type} count={count} | {details_str}")
    
    def log_error_with_context(self, error: Exception, context: Dict[str, Any] = None):
        """记录带上下文的错误"""
        if context:
            old_context = self._context.copy()
            self.add_context(**context)
            self.exception(f"Error occurred: {str(error)}")
            self._context = old_context
        else:
            self.exception(f"Error occurred: {str(error)}")
    
    # ==================== 管理方法 ====================
    
    def get_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        with self._lock:
            runtime = datetime.now() - self._stats['start_time']
            return {
                **self._stats.copy(),
                'runtime_seconds': runtime.total_seconds(),
                'error_rate': self._stats['error_count'] / max(self._stats['total_logs'], 1),
                'warning_rate': self._stats['warning_count'] / max(self._stats['total_logs'], 1)
            }
    
    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self._stats = {
                'total_logs': 0,
                'error_count': 0,
                'warning_count': 0,
                'start_time': datetime.now()
            }
    
    def flush(self):
        """刷新所有处理器"""
        for handler in self.logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
    
    def close(self):
        """关闭所有处理器"""
        for handler in self.logger.handlers:
            if hasattr(handler, 'close'):
                handler.close()
        self.logger.handlers.clear()
        self._handlers.clear()
    
    def reload_config(self):
        """重新加载配置"""
        self.config = get_config().logging
        self._setup_logger()
        self.info("Logger configuration reloaded")


# 全局日志器实例
_global_logger = None


def get_logger(name: str = "t_trade") -> UnifiedLogger:
    """获取日志器实例"""
    global _global_logger
    if _global_logger is None or _global_logger.name != name:
        _global_logger = UnifiedLogger(name)
    return _global_logger


def setup_logging(config_file: str = None):
    """设置全局日志"""
    if config_file:
        # 如果提供了配置文件，重新加载配置
        from .unified_config import get_config
        config = get_config()
        config.config_file = Path(config_file)
        config.load_config()
    
    # 获取或创建全局日志器
    logger = get_logger()
    return logger


# 便捷函数
def debug(message: str, *args, **kwargs):
    """记录调试信息"""
    get_logger().debug(message, *args, **kwargs)


def info(message: str, *args, **kwargs):
    """记录信息"""
    get_logger().info(message, *args, **kwargs)


def warning(message: str, *args, **kwargs):
    """记录警告"""
    get_logger().warning(message, *args, **kwargs)


def error(message: str, *args, **kwargs):
    """记录错误"""
    get_logger().error(message, *args, **kwargs)


def critical(message: str, *args, **kwargs):
    """记录严重错误"""
    get_logger().critical(message, *args, **kwargs)


def exception(message: str, *args, **kwargs):
    """记录异常"""
    get_logger().exception(message, *args, **kwargs)
