# 因子分析数据自动下载功能

## 概述

本文档描述了为T_TRADE量化交易系统的因子研究框架新增的数据自动下载功能。该功能解决了因子分析时遇到"No price data found for assets"警告的问题，通过在分析前自动检查和下载缺失数据，提升了用户体验和系统的自动化程度。

## 问题背景

### 原始问题
在进行因子分析时，系统会出现以下警告：
```
WARNING:factor_research.core.data_adapter:No price data found for assets: ['000001', '000002', '600000']
```

### 问题原因
- 数据库中缺少对应股票的历史价格数据
- 因子分析框架没有自动下载缺失数据的机制
- 用户需要手动下载数据后才能进行分析

## 解决方案

### 1. 架构设计

```
用户发起因子分析
        ↓
    数据完整性检查
        ↓
   [数据完整?] ——是——→ 直接开始因子计算
        ↓ 否
    询问用户是否下载
        ↓
   [用户确认?] ——否——→ 使用现有数据计算
        ↓ 是
    后台下载缺失数据
        ↓
    下载完成后开始计算
```

### 2. 核心功能模块

#### 2.1 数据可用性检查
- **位置**: `gui/panels/panel_factor_research.py`
- **方法**: `_check_data_availability()`
- **功能**: 检查指定股票和日期范围的数据完整性

```python
def _check_data_availability(self, assets, start_date, end_date):
    """检查数据可用性，返回缺失数据的股票列表"""
    missing_assets = []
    
    for asset in assets:
        price_data = data_engine._get_raw_price_data_from_db([asset], start_date, end_date)
        if price_data.empty or len(price_data) < 10:
            missing_assets.append(asset)
    
    return missing_assets
```

#### 2.2 数据下载线程
- **位置**: `gui/panels/panel_factor_research.py`
- **类**: `DataDownloadThread`
- **功能**: 后台下载缺失的股票数据

```python
class DataDownloadThread(QThread):
    """数据下载线程"""
    progress_updated = pyqtSignal(int)
    download_finished = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
```

#### 2.3 用户交互流程
- **数据检查**: 自动检查数据完整性
- **用户确认**: 弹窗询问是否下载缺失数据
- **进度显示**: 实时显示下载进度
- **状态反馈**: 更新数据状态标签

### 3. 主要改进点

#### 3.1 修改的方法

1. **`_calculate_factor()`**
   - 在因子计算前增加数据检查步骤
   - 调用`_check_and_download_missing_data()`

2. **新增方法**
   - `_check_and_download_missing_data()`: 主控制流程
   - `_check_data_availability()`: 数据可用性检查
   - `_start_data_download()`: 启动下载线程
   - `_start_factor_calculation()`: 启动因子计算
   - `_on_download_progress()`: 下载进度回调
   - `_on_download_finished()`: 下载完成回调
   - `_on_download_error()`: 下载错误回调
   - `_reset_ui_state()`: 重置UI状态

#### 3.2 UI改进
- **进度条**: 显示数据检查和下载进度
- **状态标签**: 实时更新数据状态信息
- **用户确认**: 友好的下载确认对话框
- **错误处理**: 完善的错误提示和状态重置

### 4. 技术特点

#### 4.1 异步处理
- 使用QThread进行后台数据下载
- 避免UI界面冻结
- 支持进度更新和用户取消

#### 4.2 错误处理
- 完善的异常捕获和处理
- 用户友好的错误提示
- 自动回退到现有数据计算

#### 4.3 用户体验
- 自动检测数据缺失
- 可选择的数据下载
- 实时进度反馈
- 状态信息显示

### 5. 使用流程

#### 5.1 正常流程
1. 用户输入股票代码和日期范围
2. 点击"计算因子"按钮
3. 系统自动检查数据完整性
4. 如果数据完整，直接开始计算
5. 如果数据缺失，询问用户是否下载
6. 用户确认后，后台下载数据
7. 下载完成后，自动开始因子计算

#### 5.2 异常处理
- **网络错误**: 提示用户检查网络连接
- **数据源错误**: 提示检查股票代码
- **下载失败**: 使用现有数据继续计算
- **计算错误**: 显示详细错误信息

### 6. 配置说明

#### 6.1 数据检查阈值
- **最小数据量**: 10条记录（可配置）
- **检查范围**: 用户指定的日期范围
- **检查方式**: 逐个股票检查

#### 6.2 下载参数
- **下载延迟**: 0.5秒（避免请求过频）
- **并发控制**: 单线程顺序下载
- **重试机制**: 单次尝试（可扩展）

### 7. 测试验证

#### 7.1 测试脚本
- **文件**: `test_factor_data_check.py`
- **功能**: 验证数据检查和下载功能
- **测试用例**: 
  - 数据可用性检查
  - 数据下载模拟
  - 完整流程集成测试

#### 7.2 测试方法
```bash
# 运行测试脚本
python test_factor_data_check.py
```

### 8. 未来扩展

#### 8.1 可能的改进
- **批量下载优化**: 并行下载多个股票
- **缓存机制**: 避免重复下载
- **增量更新**: 只下载缺失的日期范围
- **数据源切换**: 支持多个数据源

#### 8.2 配置化
- **下载策略**: 可配置的下载行为
- **数据阈值**: 可调整的数据完整性标准
- **UI选项**: 可选择的用户交互方式

## 总结

通过本次改进，T_TRADE系统的因子研究框架现在具备了智能的数据管理能力：

1. **自动检测**: 无需用户手动检查数据完整性
2. **智能下载**: 只下载真正缺失的数据
3. **用户友好**: 清晰的提示和进度反馈
4. **稳定可靠**: 完善的错误处理和状态管理

这一改进显著提升了用户体验，减少了手动操作，使因子分析工作流程更加自动化和智能化。
