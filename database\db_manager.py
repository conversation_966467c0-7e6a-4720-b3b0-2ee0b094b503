"""
数据库管理器

整合数据库操作，简化架构，提高性能。
遵循单一职责原则，统一数据库访问接口。
"""

import os
import threading
import time
import datetime
import pandas as pd
import logging
from typing import List, Dict, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from .base_db import BaseDB
from .quant_db import QuantDB
from .snapshot_db import SnapshotDB
from engine.data_engine.data_engine import DataEngine

logger = logging.getLogger(__name__)


class DBManager:
    """
    数据库管理器
    
    主要功能:
    - 统一数据库访问接口
    - 数据自动更新和维护
    - 缓存管理
    - 性能监控
    - 错误处理和恢复
    """
    
    def __init__(self, account_manage=None, enable_auto_update: bool = True):
        """
        初始化数据库管理器
        
        Parameters:
        -----------
        account_manage : AccountManage, optional
            账户管理对象
        enable_auto_update : bool
            是否启用自动更新
        """
        self.account_manage = account_manage
        self.enable_auto_update = enable_auto_update
        
        # 初始化数据库连接
        self.quant_db = QuantDB()

        # 初始化快照数据库
        self.snapshot_db = SnapshotDB()

        # 初始化数据引擎
        self.data_engine = DataEngine(quant_db=self.quant_db)
        
        # 内部状态
        self._update_threads = {}
        self._stop_flags = {}
        self._stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'last_update_time': None
        }
        
        # 核心股票池（沪深300）
        self._core_stock_pool = []
        
        logger.info("DBManager initialized")
    
    # ==================== 数据获取接口 ====================
    
    def get_stock_data(self, symbols: List[str], start_date: str, end_date: str,
                      fields: List[str] = None, source: str = 'db') -> pd.DataFrame:
        """
        获取股票数据（统一接口）
        
        Parameters:
        -----------
        symbols : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段
        source : str
            数据源: 'db'(数据库) 或 'api'(在线API)
            
        Returns:
        --------
        pd.DataFrame
            股票数据
        """
        try:
            if source == 'db':
                return self._get_stock_data_from_db(symbols, start_date, end_date, fields)
            else:
                return self.data_engine.get_stock_data(symbols, start_date, end_date, fields)
        except Exception as e:
            logger.error(f"Error getting stock data: {e}")
            return pd.DataFrame()
    
    def get_realtime_data(self, symbols: List[str] = None) -> pd.DataFrame:
        """获取实时数据"""
        try:
            return self.data_engine.get_realtime_data(symbols)
        except Exception as e:
            logger.error(f"Error getting realtime data: {e}")
            return pd.DataFrame()
    
    def get_hs300_stocks(self) -> List[str]:
        """获取沪深300股票列表"""
        try:
            if not self._core_stock_pool:
                self._core_stock_pool = self._fetch_hs300_stocks()
            return self._core_stock_pool.copy()
        except Exception as e:
            logger.error(f"Error getting HS300 stocks: {e}")
            return []
    
    # ==================== 数据更新接口 ====================
    
    def update_core_data(self):
        """更新核心数据（沪深300）"""
        try:
            logger.info("Starting core data update...")
            
            # 获取沪深300股票列表
            hs300_stocks = self.get_hs300_stocks()
            if not hs300_stocks:
                logger.warning("No HS300 stocks found")
                return
            
            logger.info(f"Updating data for {len(hs300_stocks)} HS300 stocks")
            
            # 检查和更新历史数据
            self._update_historical_data(hs300_stocks)
            
            # 更新实时数据
            self._update_realtime_data()
            
            self._stats['total_updates'] += 1
            self._stats['successful_updates'] += 1
            self._stats['last_update_time'] = datetime.datetime.now()
            
            logger.info("Core data update completed")
            
        except Exception as e:
            self._stats['failed_updates'] += 1
            logger.error(f"Error updating core data: {e}")
    
    def update_realtime_once(self):
        """单次更新实时数据"""
        try:
            logger.info("Updating realtime data...")
            
            # 获取实时数据
            realtime_data = self.data_engine.get_realtime_data()
            if realtime_data.empty:
                logger.warning("No realtime data retrieved")
                return
            
            # 分别保存股票和ETF数据
            stock_data = realtime_data[~realtime_data['code'].str.startswith(('5', '1', '15', '16', '51'))]
            etf_data = realtime_data[realtime_data['code'].str.startswith(('5', '1', '15', '16', '51'))]
            
            if not stock_data.empty:
                self.quant_db.save_data("stock", "realtime", stock_data, if_exists="replace")
                logger.info(f"Updated {len(stock_data)} stock realtime records")
            
            if not etf_data.empty:
                self.quant_db.save_data("etf", "realtime", etf_data, if_exists="replace")
                logger.info(f"Updated {len(etf_data)} ETF realtime records")
            
        except Exception as e:
            logger.error(f"Error updating realtime data: {e}")
    
    def start_auto_update(self, interval_minutes: int = 60):
        """启动自动更新"""
        if not self.enable_auto_update:
            logger.info("Auto update is disabled")
            return
        
        def update_worker():
            while not self._stop_flags.get('auto_update', False):
                try:
                    self.update_core_data()
                    time.sleep(interval_minutes * 60)
                except Exception as e:
                    logger.error(f"Error in auto update worker: {e}")
                    time.sleep(60)  # 出错后等待1分钟再重试
        
        if 'auto_update' not in self._update_threads:
            self._stop_flags['auto_update'] = False
            self._update_threads['auto_update'] = threading.Thread(target=update_worker, daemon=True)
            self._update_threads['auto_update'].start()
            logger.info(f"Auto update started with {interval_minutes} minutes interval")
    
    def stop_auto_update(self):
        """停止自动更新"""
        self._stop_flags['auto_update'] = True
        if 'auto_update' in self._update_threads:
            self._update_threads['auto_update'].join(timeout=5)
            del self._update_threads['auto_update']
        logger.info("Auto update stopped")
    
    # ==================== 数据库管理接口 ====================
    
    def init_database(self):
        """初始化数据库"""
        try:
            logger.info("Initializing database...")
            
            # 确保数据库文件存在
            if not os.path.exists(self.quant_db.db_path):
                logger.info("Creating new database file")
            
            # 创建基础表结构（如果需要）
            self._create_basic_tables()
            
            logger.info("Database initialization completed")
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
    
    def main_loop(self):
        """主循环（保持向后兼容）"""
        self.start_auto_update()
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            db_stats = self.quant_db.get_database_stats()
            engine_stats = self.data_engine.get_stats() if hasattr(self.data_engine, 'get_stats') else {}
            
            return {
                'database': db_stats,
                'data_engine': engine_stats,
                'manager_stats': self._stats.copy()
            }
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}
    
    # ==================== 生命周期管理 ====================
    
    def start(self):
        """启动数据库管理器"""
        try:
            logger.info("Starting DBManager...")
            
            # 初始化数据库
            self.init_database()
            
            # 更新核心数据
            self.update_core_data()
            
            # 启动自动更新
            self.start_auto_update()
            
            logger.info("DBManager started successfully")
            
        except Exception as e:
            logger.error(f"Error starting DBManager: {e}")
    
    def stop(self):
        """停止数据库管理器"""
        try:
            logger.info("Stopping DBManager...")
            
            # 停止自动更新
            self.stop_auto_update()
            
            # 关闭数据库连接
            if self.quant_db:
                self.quant_db.close()
            
            logger.info("DBManager stopped")
            
        except Exception as e:
            logger.error(f"Error stopping DBManager: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
    
    # ==================== 私有方法 ====================
    
    def _get_stock_data_from_db(self, symbols: List[str], start_date: str, 
                               end_date: str, fields: List[str] = None) -> pd.DataFrame:
        """从数据库获取股票数据"""
        try:
            all_data = []
            
            for symbol in symbols:
                # 判断是股票还是ETF
                category = "etf" if self.data_engine.is_etf_code(symbol) else "stock"
                
                # 构建查询条件
                where_conditions = [f"code='{symbol}'"]
                if start_date:
                    where_conditions.append(f"date >= '{start_date}'")
                if end_date:
                    where_conditions.append(f"date <= '{end_date}'")
                
                where_clause = " AND ".join(where_conditions)
                
                # 查询数据
                data = self.quant_db.load_data(category, "daily", where=where_clause)
                if not data.empty:
                    all_data.append(data)
            
            if all_data:
                combined_data = pd.concat(all_data, ignore_index=True)
                
                # 选择需要的字段
                if fields:
                    available_fields = [f for f in fields if f in combined_data.columns]
                    if available_fields:
                        combined_data = combined_data[available_fields]
                
                return combined_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error getting stock data from database: {e}")
            return pd.DataFrame()
    
    def _fetch_hs300_stocks(self) -> List[str]:
        """获取沪深300股票列表"""
        try:
            # 这里可以从API获取或使用预定义列表
            # 暂时返回一个示例列表
            return [
                "000001", "000002", "000858", "000895", "000938",
                "600000", "600036", "600519", "600887", "600900"
            ]
        except Exception as e:
            logger.error(f"Error fetching HS300 stocks: {e}")
            return []
    
    def _update_historical_data(self, symbols: List[str]):
        """更新历史数据"""
        try:
            logger.info(f"Updating historical data for {len(symbols)} symbols")
            
            # 检查哪些股票需要更新
            symbols_to_update = self._check_missing_data(symbols)
            
            if not symbols_to_update:
                logger.info("All historical data is up to date")
                return
            
            logger.info(f"Need to update {len(symbols_to_update)} symbols")
            
            # 批量更新
            end_date = datetime.datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime('%Y-%m-%d')
            
            for symbol in symbols_to_update:
                try:
                    data = self.data_engine.get_stock_data([symbol], start_date, end_date)
                    if not data.empty:
                        category = "etf" if self.data_engine.is_etf_code(symbol) else "stock"
                        self.quant_db.save_data(category, "daily", data, if_exists="replace")
                        logger.debug(f"Updated historical data for {symbol}")
                except Exception as e:
                    logger.warning(f"Failed to update historical data for {symbol}: {e}")
            
        except Exception as e:
            logger.error(f"Error updating historical data: {e}")
    
    def _update_realtime_data(self):
        """更新实时数据"""
        try:
            self.update_realtime_once()
        except Exception as e:
            logger.error(f"Error updating realtime data: {e}")
    
    def _check_missing_data(self, symbols: List[str]) -> List[str]:
        """检查缺失数据的股票"""
        missing_symbols = []
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        for symbol in symbols:
            try:
                category = "etf" if self.data_engine.is_etf_code(symbol) else "stock"
                data = self.quant_db.load_data(category, "daily", where=f"code='{symbol}'")
                
                if data.empty:
                    missing_symbols.append(symbol)
                else:
                    # 检查最新数据日期
                    latest_date = pd.to_datetime(data['date']).max().strftime('%Y-%m-%d')
                    if latest_date < today:
                        missing_symbols.append(symbol)
                        
            except Exception as e:
                logger.warning(f"Error checking data for {symbol}: {e}")
                missing_symbols.append(symbol)
        
        return missing_symbols
    
    def _create_basic_tables(self):
        """创建基础表结构"""
        try:
            # 这里可以创建一些基础表结构
            # 目前保持简单，不强制创建
            pass
        except Exception as e:
            logger.error(f"Error creating basic tables: {e}")
