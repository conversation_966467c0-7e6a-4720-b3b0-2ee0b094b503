"""
T_TRADE 量化交易系统主程序

优化版主程序，使用统一的配置、日志和异常处理系统。
遵循依赖注入原则，提高可测试性和可维护性。
"""

import sys
import signal
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# 导入统一的通用系统
from common import (
    initialize_common_systems, cleanup_common_systems,
    get_config, get_performance_manager,
    handle_exceptions, ErrorContext
)

# 导入核心模块
from gui.mainframe_v2 import MainFrameV2
from database.db_manage import DBManage
from portfolio.account.account_manage import AccountManage
from portfolio.position.position_manager import PositionManage
from engine.trade_engine.trade_engine import TradeEngine
from engine.trade_engine.trade_broker import TradeBroker
from plot.trade_plot import TradePlot


class TTradeApplication:
    """
    T_TRADE应用程序主类

    负责应用程序的初始化、运行和清理
    """

    def __init__(self):
        self.app = None
        self.main_frame = None
        self.dependencies = {}
        self.systems = None
        self.logger = None

    def initialize(self) -> bool:
        """
        初始化应用程序

        Returns:
        --------
        bool
            是否初始化成功
        """
        try:
            with ErrorContext("application_initialization"):
                # 初始化通用系统
                self.systems = initialize_common_systems()
                if not self.systems or not isinstance(self.systems, dict):
                    print("Failed to initialize common systems")
                    return False

                self.logger = self.systems.get('logger')
                if not self.logger:
                    print("Failed to initialize logger")
                    return False
                self.logger.info("Starting T_TRADE application initialization...")

                # 初始化依赖对象
                if not self._initialize_dependencies():
                    return False

                # 初始化Qt应用程序
                if not self._initialize_qt_app():
                    return False

                # 初始化主界面
                if not self._initialize_main_frame():
                    return False

                # 设置信号处理
                self._setup_signal_handlers()

                self.logger.info("T_TRADE application initialized successfully")
                return True

        except Exception as e:
            print(f"Error initializing application: {e}")
            return False

    def _initialize_dependencies(self) -> bool:
        """初始化依赖对象"""
        try:
            self.logger.info("Initializing core dependencies...")

            # 账户管理
            account_manager = AccountManage()

            # 数据库管理
            db_manager = DBManage(account_manage=account_manager)

            # 交易相关
            trade_broker = TradeBroker()
            position_manager = PositionManage(broker=trade_broker)

            # 绘图工具
            trade_ploter = TradePlot(db_manage=db_manager)

            # 交易引擎
            trade_engine = TradeEngine(
                trade_broker=trade_broker,
                position_manager=position_manager,
                db_manage=db_manager,
                trade_plot=trade_ploter
            )

            # 保存依赖对象
            self.dependencies = {
                'account_manage': account_manager,
                'db_manage': db_manager,
                'trade_broker': trade_broker,
                'position_manager': position_manager,
                'trade_ploter': trade_ploter,
                'trade_engine': trade_engine
            }

            self.logger.info("Core dependencies initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing dependencies: {e}")
            return False

    def _initialize_qt_app(self) -> bool:
        """初始化Qt应用程序"""
        try:
            self.logger.info("Initializing Qt application...")

            # 设置 Qt 属性以支持 WebEngine
            QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)

            self.app = QApplication(sys.argv)
            self.app.setApplicationName("T_TRADE")
            self.app.setApplicationVersion("2.1.0")
            self.app.setOrganizationName("T_Trade Team")

            # 设置应用程序样式
            config = get_config()
            if config.gui.theme != "default":
                # 这里可以设置自定义主题
                pass

            self.logger.info("Qt application initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing Qt application: {e}")
            return False

    def _initialize_main_frame(self) -> bool:
        """初始化主界面"""
        try:
            self.logger.info("Initializing main frame...")

            # 使用重构的主框架
            self.main_frame = MainFrameV2(dependencies=self.dependencies)

            self.logger.info("Main frame initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing main frame: {e}")
            return False

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def run(self) -> int:
        """
        运行应用程序

        Returns:
        --------
        int
            退出代码
        """
        try:
            if not self.main_frame:
                self.logger.error("Main frame not initialized")
                return 1

            self.logger.info("Starting T_TRADE application...")

            # 启动数据库后台更新
            if 'db_manage' in self.dependencies:
                db_manager = self.dependencies['db_manage']
                if hasattr(db_manager, 'start'):
                    db_manager.start()

            # 显示主界面
            self.main_frame.show()

            # 启动性能监控
            performance_manager = get_performance_manager()
            performance_manager.start_monitoring()

            # 运行Qt事件循环
            exit_code = self.app.exec_()

            self.logger.info(f"Application exited with code: {exit_code}")
            return exit_code

        except Exception as e:
            self.logger.error(f"Error running application: {e}")
            return 1
        finally:
            self.shutdown()

    def shutdown(self):
        """关闭应用程序"""
        try:
            self.logger.info("Shutting down T_TRADE application...")

            # 停止数据库管理器
            if 'db_manage' in self.dependencies:
                db_manager = self.dependencies['db_manage']
                if hasattr(db_manager, 'stop'):
                    db_manager.stop()

            # 关闭主界面
            if self.main_frame:
                self.main_frame.close()

            # 退出Qt应用程序
            if self.app:
                self.app.quit()

            # 清理通用系统
            cleanup_common_systems()

        except Exception as e:
            print(f"Error during shutdown: {e}")


@handle_exceptions(default_return=1, log_level="critical")
def main() -> int:
    """
    主函数

    Returns:
    --------
    int
        退出代码
    """
    app = TTradeApplication()

    if not app.initialize():
        print("Failed to initialize application")
        return 1

    return app.run()


if __name__ == "__main__":
    sys.exit(main())