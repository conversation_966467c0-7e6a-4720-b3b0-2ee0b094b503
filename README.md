# T-Trade 量化交易系统

一个功能完整的量化交易系统，集成了数据管理、因子研究、策略回测和实时交易功能。

## 🚀 主要功能

### 📊 因子研究模块
- **单因子分析**: 基于alphalens的因子有效性分析
- **因子分布图**: 可视化因子数据分布和统计信息
- **IC分析**: 信息系数分析和时序图表
- **分位数收益分析**: 因子分位数组合收益分析
- **换手率分析**: 因子相关的换手率统计

### 💹 数据引擎
- **实时数据**: 支持多种数据源的实时行情数据
- **历史数据**: 完整的历史价格和基本面数据
- **数据存储**: 基于DuckDB的高性能数据存储
- **数据更新**: 自动化的数据更新和维护机制

### 🎯 策略系统
- **策略开发**: 灵活的策略开发框架
- **回测引擎**: 高性能的历史回测系统
- **风险管理**: 完整的风险控制和资金管理
- **实时交易**: 支持实时策略执行

### 🖥️ 图形界面
- **现代化GUI**: 基于tkinter的用户友好界面
- **实时监控**: 实时交易和策略监控面板
- **因子研究**: 专门的因子研究可视化界面
- **中文支持**: 完整的中文字体显示支持

## 📁 项目结构

```
t_trade/
├── data_engine/          # 数据引擎模块
├── factor_research/      # 因子研究模块
│   ├── core/            # 核心分析功能
│   ├── factors/         # 因子定义
│   └── utils/           # 工具函数
├── gui/                 # 图形用户界面
│   ├── panels/          # 各功能面板
│   ├── widgets/         # 自定义控件
│   └── elements/        # UI元素
├── engine/              # 核心引擎
│   ├── backtest_engine/ # 回测引擎
│   ├── trade_engine/    # 交易引擎
│   └── data_engine/     # 数据引擎
├── strategies/          # 交易策略
├── portfolio/           # 投资组合管理
├── database/            # 数据库模块
├── test/                # 测试文件
├── docs/                # 文档
├── scripts/             # 工具脚本
└── main.py             # 主程序入口
```

## 🛠️ 安装和配置

### 环境要求
- Python 3.8+
- Windows 10/11 (推荐)
- 8GB+ RAM
- 10GB+ 可用磁盘空间

### 依赖安装
```bash
# 安装核心依赖
pip install -r requirements-core.txt

# 安装完整依赖（包括可选组件）
pip install -r requirements.txt
```

### 核心依赖
- `pandas` - 数据处理
- `numpy` - 数值计算
- `matplotlib` - 图表绘制
- `duckdb` - 数据存储
- `tkinter` - GUI界面
- `requests` - 网络请求

### 可选依赖
- `alphalens` - 因子分析（推荐）
- `seaborn` - 高级图表
- `scipy` - 科学计算

## 🚀 快速开始

### 1. 启动主程序
```bash
python main.py
```

### 2. 因子研究
1. 打开因子研究面板
2. 选择股票池（默认CSI 300）
3. 计算因子数据
4. 查看分析结果

### 3. 数据管理
- 系统会自动下载和更新CSI 300成分股数据
- 支持手动触发数据更新
- 数据存储在`data/`目录下

## 📊 因子研究功能

### 支持的分析类型
- **因子分布分析**: 统计分布、分位数信息
- **IC分析**: 信息系数时序分析
- **分位数收益**: 按因子值分组的收益分析
- **换手率分析**: 因子相关的组合换手率

### 可视化图表
- 因子分布直方图和统计表格
- IC时序图和累积IC图
- 分位数收益柱状图
- 换手率时序图

## 🔧 配置说明

### 配置文件
- `config/development.yaml` - 开发环境配置
- `config/production.yaml` - 生产环境配置

### 主要配置项
```yaml
database:
  path: "data/"
  
data_source:
  provider: "default"
  update_interval: 3600
  
factor_research:
  default_universe: "CSI300"
  analysis_periods: [1, 5, 10]
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
python test/run_tests.py

# 运行单元测试
python -m pytest test/unit/

# 运行集成测试
python -m pytest test/integration/
```

### 测试覆盖
- 核心业务逻辑: 90%+
- 数据处理模块: 80%+
- GUI模块: 基本功能测试

## 📝 开发规范

### 代码规范
- 遵循PEP 8编码规范
- 使用类型提示
- 完整的文档字符串
- 详见 `docs/rules.md`

### 中文字体支持
- 所有matplotlib图表支持中文显示
- 自动检测和配置中文字体
- 支持SimHei、Microsoft YaHei、SimSun字体

## 🔍 故障排除

### 常见问题

**Q: 中文显示为方框？**
A: 系统会自动配置中文字体，如仍有问题请重启程序。

**Q: 数据下载失败？**
A: 检查网络连接，系统会自动重试失败的下载。

**Q: 因子分析结果异常？**
A: 确保数据完整性，检查因子计算逻辑。

### 日志文件
- `logs/development.log` - 一般日志
- `logs/development_error.log` - 错误日志

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>
cd t_trade

# 安装开发依赖
pip install -r requirements.txt

# 运行测试确保环境正常
python test/run_tests.py
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 LICENSE 文件

## 📞 支持

- 📧 邮箱: [<EMAIL>]
- 📖 文档: `docs/` 目录
- 🐛 问题反馈: GitHub Issues

## 🔄 更新日志

### v1.0.0 (2025-06-28)
- ✅ 完整的因子研究模块
- ✅ 中文字体显示支持
- ✅ 数据引擎和存储系统
- ✅ 现代化GUI界面
- ✅ 完整的测试覆盖

---

**最后更新**: 2025-06-28
**版本**: v1.0.0
