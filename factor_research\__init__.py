"""
单因子研究框架（优化版）

基于alphalens构建的单因子研究框架，提供完整的因子分析工具链。
采用统一架构，使用FactorResearch作为主要接口，整合了因子计算、分析和可视化功能。

主要功能:
- 统一的因子研究接口 (FactorResearch)
- 因子计算和管理
- 基于alphalens的因子分析
- 丰富的可视化功能
- 与现有数据库系统集成
- 为多因子研究预留扩展接口

使用示例:
    from factor_research import FactorResearch

    # 初始化因子研究框架
    factor_research = FactorResearch(db_manage=db_manage)

    # 计算因子
    factor_data = factor_research.calculate_factor(
        factor_name="MomentumFactor",
        assets=["000001", "000002"],
        start_date="2023-01-01",
        end_date="2023-12-31"
    )

    # 分析因子
    results = factor_research.analyze_factor(factor_data)

    # 批量分析多个因子
    factor_data_dict = factor_research.calculate_multiple_factors(
        factor_names=["MomentumFactor", "RSIFactor"],
        assets=["000001", "000002"],
        start_date="2023-01-01",
        end_date="2023-12-31"
    )

    comparison_results = factor_research.compare_factors(factor_data_dict)
"""

# 主要导出 - 统一接口
from .core.factor_research import FactorResearch

# 向后兼容导出（已弃用，建议使用FactorResearch）
from .core.data_adapter import DataAdapter
from .core.factor_engine import FactorEngine
from .core.factor_analyzer import FactorAnalyzer
from .core.visualizer import FactorVisualizer

# 因子类导出
from .factors.base import BaseFactor
from .factors.momentum import MomentumFactor, RSIFactor, PriceVolumeTrendFactor

# 工具类导出
from .utils.config import FactorConfig

__version__ = "2.1.0"
__author__ = "T_Trade Team"

__all__ = [
    # 主要接口（推荐使用）
    "FactorResearch",

    # 向后兼容接口（已弃用）
    "DataAdapter",
    "FactorEngine",
    "FactorAnalyzer",
    "FactorVisualizer",

    # 因子类
    "BaseFactor",
    "MomentumFactor",
    "RSIFactor",
    "PriceVolumeTrendFactor",

    # 工具类
    "FactorConfig"
]
