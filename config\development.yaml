# 开发环境配置
database:
  path: "data/dev_quant_data.duckdb"
  cache_size: 100
  connection_timeout: 10
  backup_enabled: false

factor_research:
  factor_calculation:
    parallel_enabled: false
    max_workers: 2
  visualization:
    dpi: 150
    save_format: "png"

trading:
  data_update:
    realtime_interval: 10  # 开发环境更新间隔更长

logging:
  level: "DEBUG"
  handlers:
    console:
      level: "DEBUG"
    file:
      level: "DEBUG"

performance:
  monitoring:
    enabled: true
    alert_thresholds:
      memory_usage: 0.9
      cpu_usage: 0.95
  caching:
    memory_cache_size: "100MB"
    disk_cache_size: "500MB"
