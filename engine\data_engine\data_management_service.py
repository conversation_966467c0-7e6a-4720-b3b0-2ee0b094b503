"""
数据管理服务

提供统一的数据管理接口，整合分层数据管理器和现有数据引擎。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Tuple, Union
import logging
import threading
import time
from pathlib import Path

from .tiered_data_manager import TieredDataManager
from .data_engine import DataEngine

logger = logging.getLogger(__name__)


class DataManagementService:
    """数据管理服务"""
    
    def __init__(self, db_manage, enable_tiered_management: bool = True):
        """
        初始化数据管理服务
        
        Parameters:
        -----------
        db_manage : DBManage
            数据库管理器
        enable_tiered_management : bool
            是否启用分层数据管理
        """
        self.db_manage = db_manage
        self.enable_tiered_management = enable_tiered_management
        
        # 初始化数据引擎
        self.data_engine = DataEngine(quant_db=db_manage.quant_db if db_manage else None)
        
        # 初始化分层数据管理器
        if enable_tiered_management:
            self.tiered_manager = TieredDataManager(
                db_manage=db_manage,
                data_engine=self.data_engine
            )
        else:
            self.tiered_manager = None
        
        # 服务状态
        self.service_status = {
            'initialized': True,
            'tiered_management_enabled': enable_tiered_management,
            'last_update': datetime.now(),
            'total_requests': 0,
            'successful_requests': 0
        }
        
        logger.info(f"DataManagementService initialized (tiered: {enable_tiered_management})")
    
    def get_stock_data(self, assets: List[str], start_date: str, end_date: str, 
                      fields: List[str] = None, use_tiered: bool = None) -> pd.DataFrame:
        """
        获取股票数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段
        use_tiered : bool, optional
            是否使用分层管理，None时使用默认设置
            
        Returns:
        --------
        pd.DataFrame
            股票数据，MultiIndex格式
        """
        self.service_status['total_requests'] += 1
        
        try:
            # 决定是否使用分层管理
            if use_tiered is None:
                use_tiered = self.enable_tiered_management
            
            if use_tiered and self.tiered_manager:
                # 使用分层数据管理
                data = self.tiered_manager.get_tiered_data(
                    assets=assets,
                    start_date=start_date,
                    end_date=end_date,
                    fields=fields,
                    auto_download=True
                )
            else:
                # 使用传统方式获取数据
                data = self._get_data_traditional(assets, start_date, end_date, fields)
            
            self.service_status['successful_requests'] += 1
            self.service_status['last_update'] = datetime.now()
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting stock data: {e}")
            # 返回空的MultiIndex DataFrame
            if fields is None:
                fields = ['open', 'high', 'low', 'close', 'volume', 'amount']
            index = pd.MultiIndex.from_tuples([], names=['date', 'asset'])
            return pd.DataFrame(index=index, columns=fields)
    
    def _get_data_traditional(self, assets: List[str], start_date: str, end_date: str, 
                            fields: List[str] = None) -> pd.DataFrame:
        """传统方式获取数据"""
        if fields is None:
            fields = ['open', 'high', 'low', 'close', 'volume', 'amount']
        
        data_frames = []
        
        for asset in assets:
            try:
                # 使用data_engine下载数据
                df = self.data_engine._get_stock_hist_info(asset, "daily", "")
                
                if not df.empty:
                    # 过滤日期范围
                    df['date'] = pd.to_datetime(df['date'])
                    mask = (df['date'] >= start_date) & (df['date'] <= end_date)
                    df = df[mask]
                    
                    # 添加资产标识
                    df['asset'] = asset
                    
                    # 设置MultiIndex
                    df = df.set_index(['date', 'asset'])
                    
                    # 只保留需要的字段
                    available_fields = [f for f in fields if f in df.columns]
                    if available_fields:
                        df = df[available_fields]
                        data_frames.append(df)
                
            except Exception as e:
                logger.warning(f"Error getting data for {asset}: {e}")
        
        if data_frames:
            result = pd.concat(data_frames, axis=0)
            result = result.sort_index()
            return result
        else:
            # 返回空的MultiIndex DataFrame
            index = pd.MultiIndex.from_tuples([], names=['date', 'asset'])
            return pd.DataFrame(index=index, columns=fields)
    
    def check_data_status(self, assets: List[str], start_date: str, end_date: str) -> Dict:
        """
        检查数据状态
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns:
        --------
        Dict
            数据状态报告
        """
        if self.tiered_manager:
            return self.tiered_manager.check_data_availability(assets, start_date, end_date)
        else:
            # 简化的状态检查
            return {
                'total_assets': len(assets),
                'tier_distribution': {1: [], 2: [], 3: assets},
                'data_status': {asset: {'available': False, 'tier': 3} for asset in assets},
                'missing_data': assets,
                'download_required': True,
                'estimated_download_time': len(assets) * 2
            }
    
    def get_data_strategy_info(self, assets: List[str], start_date: str, end_date: str) -> str:
        """
        获取数据策略信息
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns:
        --------
        str
            策略信息文本
        """
        status = self.check_data_status(assets, start_date, end_date)
        
        info_lines = []
        info_lines.append("📊 数据获取策略分析")
        info_lines.append(f"总股票数: {status['total_assets']}")
        
        if self.enable_tiered_management:
            tier_dist = status['tier_distribution']
            if tier_dist[1]:
                info_lines.append(f"✅ 核心股票池: {len(tier_dist[1])}只 (沪深300)")
            if tier_dist[2]:
                info_lines.append(f"💾 热门股票池: {len(tier_dist[2])}只")
            if tier_dist[3]:
                info_lines.append(f"⬇️ 临时下载: {len(tier_dist[3])}只")
        
        missing_count = len(status['missing_data'])
        if missing_count > 0:
            info_lines.append(f"⬇️ 需要下载: {missing_count}只")
            info_lines.append(f"   预计时间: {status['estimated_download_time']}秒")
            
            # 显示部分股票代码
            missing_codes = status['missing_data'][:5]
            info_lines.append(f"   股票代码: {', '.join(missing_codes)}")
            if len(status['missing_data']) > 5:
                info_lines.append(f"   等{len(status['missing_data'])}只股票...")
        else:
            info_lines.append("🚀 所有数据已就绪，可立即开始分析")
        
        return "\n".join(info_lines)
    
    def preload_core_data(self, years: int = 3) -> Dict:
        """
        预加载核心数据
        
        Parameters:
        -----------
        years : int
            预加载年数
            
        Returns:
        --------
        Dict
            预加载结果
        """
        if self.tiered_manager:
            return self.tiered_manager.preload_core_data(years)
        else:
            return {
                'error': 'Tiered management not enabled',
                'total_stocks': 0,
                'downloaded': 0,
                'failed': 0
            }
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        stats = {
            'service_status': self.service_status.copy(),
            'tiered_management_enabled': self.enable_tiered_management
        }
        
        if self.tiered_manager:
            tiered_stats = self.tiered_manager.get_performance_stats()
            stats['tiered_stats'] = tiered_stats
        
        return stats
    
    def get_hs300_stocks(self) -> List[str]:
        """获取沪深300股票列表"""
        if self.tiered_manager:
            return self.tiered_manager.tier_config['tier1']['stocks']
        else:
            # 返回一个简化的列表
            return [
                "000001", "000002", "600000", "600036", "600519", "000858", 
                "002415", "300059", "300750", "688981", "000333", "000651",
                "002032", "002050", "600690", "000921", "002508", "600418"
            ]
    
    def add_to_hot_stocks(self, assets: List[str]):
        """添加股票到热门股票池"""
        if self.tiered_manager:
            hot_stocks = set(self.tiered_manager.tier_config['tier2']['stocks'])
            hot_stocks.update(assets)
            self.tiered_manager.tier_config['tier2']['stocks'] = list(hot_stocks)
            logger.info(f"Added {len(assets)} stocks to hot stocks pool")
    
    def get_tier_info(self) -> Dict:
        """获取分层信息"""
        if self.tiered_manager:
            return {
                'tier1': {
                    'name': self.tiered_manager.tier_config['tier1']['name'],
                    'count': len(self.tiered_manager.tier_config['tier1']['stocks']),
                    'description': self.tiered_manager.tier_config['tier1']['description']
                },
                'tier2': {
                    'name': self.tiered_manager.tier_config['tier2']['name'],
                    'count': len(self.tiered_manager.tier_config['tier2']['stocks']),
                    'description': self.tiered_manager.tier_config['tier2']['description']
                },
                'tier3': {
                    'name': self.tiered_manager.tier_config['tier3']['name'],
                    'description': self.tiered_manager.tier_config['tier3']['description']
                }
            }
        else:
            return {
                'message': 'Tiered management not enabled',
                'mode': 'traditional'
            }
    
    def enable_tiered_management(self):
        """启用分层管理"""
        if not self.tiered_manager:
            self.tiered_manager = TieredDataManager(
                db_manage=self.db_manage,
                data_engine=self.data_engine
            )
            self.enable_tiered_management = True
            self.service_status['tiered_management_enabled'] = True
            logger.info("Tiered management enabled")
    
    def disable_tiered_management(self):
        """禁用分层管理"""
        self.tiered_manager = None
        self.enable_tiered_management = False
        self.service_status['tiered_management_enabled'] = False
        logger.info("Tiered management disabled")
