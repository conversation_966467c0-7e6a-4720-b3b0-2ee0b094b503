data_engine:
  cache_enabled: true
  cache_size_limit: 100
  max_workers: 4
  rate_limit_per_second: 10
  request_timeout: 30
  retry_times: 3
database:
  backup_enabled: true
  backup_interval_hours: 24
  base_dir: data
  hist_db_name: hist_data.duckdb
  quant_db_name: quant_data.duckdb
  realtime_db_name: realtime_data.duckdb
  snapshot_db_name: snapshot_data.duckdb
factor_research:
  alphalens_timeout: 300
  cache_enabled: true
  default_periods:
  - 1
  - 5
  - 10
  - 20
  default_quantiles: 5
  max_loss: 0.35
  parallel_enabled: false
gui:
  auto_refresh_interval: 60
  chart_dpi: 100
  font_family: SimHei
  font_size: 10
  theme: default
  window_height: 900
  window_width: 1400
logging:
  backup_count: 5
  console_enabled: true
  file_enabled: true
  file_path: logs/t_trade.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  max_file_size: ********
trading:
  commission_rate: 0.0003
  default_account: test
  max_position_ratio: 0.95
  min_commission: 5.0
  risk_control_enabled: true
  stamp_duty_rate: 0.001
