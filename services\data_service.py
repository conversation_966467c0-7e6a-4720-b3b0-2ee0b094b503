#!/usr/bin/env python3
"""
独立数据服务

专门负责数据下载、更新和管理，与研究模块分离。
"""

import sys
import os
import time
import threading
import schedule
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DataService:
    """独立数据服务"""
    
    def __init__(self):
        """初始化数据服务"""
        self.running = False
        self.service_thread = None
        
        # 初始化数据库管理器
        self._init_database()
        
        # 服务状态
        self.status = {
            'start_time': None,
            'last_update': None,
            'total_updates': 0,
            'failed_updates': 0,
            'current_task': None
        }
        
        logger.info("DataService initialized")
    
    def _init_database(self):
        """初始化数据库连接"""
        try:
            from database.db_manage import DBManage
            from portfolio.account.account_manage import AccountManage
            
            self.account_manage = AccountManage()
            self.db_manage = DBManage(self.account_manage)
            
            # 初始化数据库
            self.db_manage.init_database()
            
            logger.info("Database connection established")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def start_service(self):
        """启动数据服务"""
        if self.running:
            logger.warning("Data service is already running")
            return
        
        self.running = True
        self.status['start_time'] = datetime.now()
        
        # 启动服务线程
        self.service_thread = threading.Thread(target=self._run_service, daemon=True)
        self.service_thread.start()
        
        logger.info("Data service started")
    
    def stop_service(self):
        """停止数据服务"""
        if not self.running:
            logger.warning("Data service is not running")
            return
        
        self.running = False
        
        if self.service_thread:
            self.service_thread.join(timeout=10)
        
        logger.info("Data service stopped")
    
    def _run_service(self):
        """运行数据服务主循环"""
        logger.info("Data service main loop started")
        
        # 配置定时任务
        self._schedule_tasks()
        
        # 启动时执行一次完整更新
        self._initial_data_update()
        
        # 主循环
        while self.running:
            try:
                # 执行定时任务
                schedule.run_pending()
                
                # 短暂休眠
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"Error in service main loop: {e}")
                time.sleep(60)
        
        logger.info("Data service main loop stopped")
    
    def _schedule_tasks(self):
        """配置定时任务"""
        # 每天早上8点更新沪深300核心数据
        schedule.every().day.at("08:00").do(self._update_core_data)
        
        # 交易时间每5分钟更新实时数据
        schedule.every(5).minutes.do(self._update_realtime_data_if_trading_hours)
        
        # 每周日凌晨2点进行数据清理和维护
        schedule.every().sunday.at("02:00").do(self._maintenance_tasks)
        
        logger.info("Scheduled tasks configured")
    
    def _initial_data_update(self):
        """启动时的初始数据更新"""
        logger.info("Starting initial data update...")
        
        try:
            self.status['current_task'] = "初始数据更新"
            
            # 检查和更新核心数据
            self._update_core_data()
            
            # 更新实时数据
            self._update_realtime_data()
            
            logger.info("Initial data update completed")
            
        except Exception as e:
            logger.error(f"Initial data update failed: {e}")
            self.status['failed_updates'] += 1
        finally:
            self.status['current_task'] = None
    
    def _update_core_data(self):
        """更新核心数据（沪深300）"""
        logger.info("Starting core data update...")
        
        try:
            self.status['current_task'] = "更新核心数据"
            
            # 使用现有的沪深300数据下载功能
            self.db_manage.download_hs300_core_data()
            
            self.status['total_updates'] += 1
            self.status['last_update'] = datetime.now()
            
            logger.info("Core data update completed")
            
        except Exception as e:
            logger.error(f"Core data update failed: {e}")
            self.status['failed_updates'] += 1
        finally:
            self.status['current_task'] = None
    
    def _update_realtime_data(self):
        """更新实时数据"""
        logger.info("Starting realtime data update...")
        
        try:
            self.status['current_task'] = "更新实时数据"
            
            # 使用现有的实时数据更新功能
            self.db_manage.update_realtime_once()
            
            self.status['total_updates'] += 1
            self.status['last_update'] = datetime.now()
            
            logger.info("Realtime data update completed")
            
        except Exception as e:
            logger.error(f"Realtime data update failed: {e}")
            self.status['failed_updates'] += 1
        finally:
            self.status['current_task'] = None
    
    def _update_realtime_data_if_trading_hours(self):
        """仅在交易时间更新实时数据"""
        if self._is_trading_hours():
            self._update_realtime_data()
        else:
            logger.debug("Not in trading hours, skipping realtime update")
    
    def _is_trading_hours(self) -> bool:
        """检查是否在交易时间"""
        now = datetime.now()
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        # 检查时间范围 (9:30-11:30, 13:00-15:00)
        current_time = now.time()
        
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()
        
        return (morning_start <= current_time <= morning_end or 
                afternoon_start <= current_time <= afternoon_end)
    
    def _maintenance_tasks(self):
        """维护任务"""
        logger.info("Starting maintenance tasks...")
        
        try:
            self.status['current_task'] = "系统维护"
            
            # 数据库清理
            self._cleanup_old_data()
            
            # 数据完整性检查
            self._check_data_integrity()
            
            # 性能统计
            self._generate_performance_report()
            
            logger.info("Maintenance tasks completed")
            
        except Exception as e:
            logger.error(f"Maintenance tasks failed: {e}")
            self.status['failed_updates'] += 1
        finally:
            self.status['current_task'] = None
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            # 清理超过5年的历史数据
            cutoff_date = (datetime.now() - timedelta(days=5*365)).strftime('%Y-%m-%d')
            
            delete_sql = f"DELETE FROM stock_daily WHERE date < '{cutoff_date}'"
            self.db_manage.quant_db.conn.execute(delete_sql)
            
            logger.info(f"Cleaned up data older than {cutoff_date}")
            
        except Exception as e:
            logger.error(f"Data cleanup failed: {e}")
    
    def _check_data_integrity(self):
        """检查数据完整性"""
        try:
            # 检查数据表状态
            tables = ['stock_daily', 'stock_realtime']
            
            for table in tables:
                try:
                    count_result = self.db_manage.quant_db.conn.execute(
                        f"SELECT COUNT(*) FROM {table}"
                    ).fetchone()
                    count = count_result[0] if count_result else 0
                    
                    logger.info(f"Table {table}: {count} records")
                    
                except Exception as e:
                    logger.warning(f"Failed to check table {table}: {e}")
            
        except Exception as e:
            logger.error(f"Data integrity check failed: {e}")
    
    def _generate_performance_report(self):
        """生成性能报告"""
        try:
            uptime = datetime.now() - self.status['start_time'] if self.status['start_time'] else timedelta(0)
            success_rate = (self.status['total_updates'] - self.status['failed_updates']) / max(self.status['total_updates'], 1) * 100
            
            report = f"""
数据服务性能报告
================
运行时间: {uptime}
总更新次数: {self.status['total_updates']}
失败次数: {self.status['failed_updates']}
成功率: {success_rate:.1f}%
最后更新: {self.status['last_update']}
当前任务: {self.status['current_task'] or '空闲'}
"""
            
            logger.info(report)
            
            # 保存报告到文件
            with open('logs/data_service_report.txt', 'w', encoding='utf-8') as f:
                f.write(report)
            
        except Exception as e:
            logger.error(f"Performance report generation failed: {e}")
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            'running': self.running,
            'status': self.status.copy(),
            'next_scheduled_tasks': [str(job) for job in schedule.jobs]
        }
    
    def force_update(self, data_type: str = 'all'):
        """强制更新数据"""
        logger.info(f"Force update requested: {data_type}")
        
        try:
            if data_type in ['all', 'core']:
                self._update_core_data()
            
            if data_type in ['all', 'realtime']:
                self._update_realtime_data()
            
            logger.info(f"Force update completed: {data_type}")
            
        except Exception as e:
            logger.error(f"Force update failed: {e}")
            raise


def main():
    """主函数"""
    print("🚀 启动独立数据服务")
    print("=" * 50)
    
    # 创建数据服务
    data_service = DataService()
    
    try:
        # 启动服务
        data_service.start_service()
        
        print("✅ 数据服务已启动")
        print("📊 服务功能:")
        print("  - 每天8点更新沪深300核心数据")
        print("  - 交易时间每5分钟更新实时数据")
        print("  - 每周日凌晨2点进行系统维护")
        print("  - 提供数据状态监控")
        
        print("\n💡 使用说明:")
        print("  - 研究模块只需要读取数据库")
        print("  - 无需担心数据下载和更新")
        print("  - 数据服务在后台自动运行")
        
        print("\n按 Ctrl+C 停止服务...")
        
        # 保持服务运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号")
        data_service.stop_service()
        print("✅ 数据服务已停止")
    
    except Exception as e:
        print(f"❌ 数据服务异常: {e}")
        data_service.stop_service()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
