"""
分层数据管理器

实现分层数据管理策略：
- 第一层：核心股票池（沪深300）预下载数据
- 第二层：扩展股票池按需下载数据  
- 第三层：智能缓存管理
- 第四层：实时数据更新
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Tuple, Union
import logging
import os
import json
import threading
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


class TieredDataManager:
    """分层数据管理器"""
    
    def __init__(self, db_manage, data_engine, cache_dir="data/tiered_cache"):
        """
        初始化分层数据管理器
        
        Parameters:
        -----------
        db_manage : DBManage
            数据库管理器
        data_engine : DataEngine
            数据引擎
        cache_dir : str
            缓存目录
        """
        self.db_manage = db_manage
        self.data_engine = data_engine
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 分层配置
        self.tier_config = self._init_tier_config()
        
        # 数据状态管理
        self.data_status = {}
        self.download_queue = []
        self.download_lock = threading.Lock()
        
        # 性能统计
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'downloads': 0,
            'total_requests': 0
        }
        
        logger.info("TieredDataManager initialized")
    
    def _init_tier_config(self) -> Dict:
        """初始化分层配置"""
        config = {
            # 第一层：核心股票池（沪深300）
            'tier1': {
                'name': '核心股票池',
                'description': '沪深300成分股',
                'stocks': self._load_hs300_stocks(),
                'preload_years': 3,
                'update_frequency': 'daily',
                'priority': 1
            },
            
            # 第二层：热门股票池
            'tier2': {
                'name': '热门股票池',
                'description': '用户常用股票',
                'stocks': [],
                'cache_days': 90,
                'update_frequency': 'weekly',
                'priority': 2
            },
            
            # 第三层：临时股票池
            'tier3': {
                'name': '临时股票池',
                'description': '按需下载股票',
                'stocks': [],
                'cache_days': 30,
                'update_frequency': 'on_demand',
                'priority': 3
            },
            
            # 第四层：实时数据
            'tier4': {
                'name': '实时数据',
                'description': '当日实时行情',
                'update_frequency': 'realtime',
                'priority': 4
            }
        }
        
        return config
    
    def _load_hs300_stocks(self) -> List[str]:
        """加载沪深300股票列表"""
        # 从缓存文件加载或生成沪深300列表
        cache_file = self.cache_dir / "hs300_stocks.json"
        
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    stocks = json.load(f)
                logger.info(f"Loaded {len(stocks)} HS300 stocks from cache")
                return stocks
            except Exception as e:
                logger.warning(f"Failed to load HS300 cache: {e}")
        
        # 生成沪深300股票列表（简化版本）
        hs300_stocks = self._generate_hs300_list()
        
        # 保存到缓存
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(hs300_stocks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save HS300 cache: {e}")
        
        return hs300_stocks
    
    def _generate_hs300_list(self) -> List[str]:
        """生成沪深300股票列表"""
        # 这里使用一个简化的沪深300列表
        # 实际应用中可以从数据源获取最新的成分股
        hs300_core = [
            # 银行股
            "000001", "600000", "600036", "601318", "601398", "000002", "600015", "600016",
            "600019", "601166", "601169", "601288", "601328", "601818", "601838", "601939",
            
            # 非银金融
            "601318", "601601", "601628", "601688", "601788", "601878", "601881", "601901",
            
            # 食品饮料
            "000858", "600519", "000568", "002304", "603288", "000596", "600809", "600887",
            
            # 家用电器
            "000333", "000651", "002032", "002050", "600690", "000921", "002508", "600418",
            
            # 医药生物
            "000661", "002007", "002422", "002821", "300015", "300142", "300347", "300601",
            
            # 电子
            "000725", "002415", "002456", "002475", "300059", "300750", "688981", "000063",
            
            # 计算机
            "000977", "002230", "002410", "002415", "300033", "300454", "600570", "600588",
            
            # 通信
            "000063", "600050", "600198", "600487", "600776", "000997", "002017", "600522",
            
            # 汽车
            "000625", "002594", "600104", "600166", "600741", "601238", "601633", "601799",
            
            # 房地产
            "000002", "000069", "000656", "001979", "600048", "600340", "600383", "600606",
            
            # 建筑材料
            "000877", "600585", "600801", "601636", "000401", "002271", "600176", "600668",
            
            # 钢铁
            "000709", "000717", "000825", "000898", "600019", "600022", "600126", "600231",
            
            # 有色金属
            "000060", "000630", "000831", "000878", "002460", "600362", "600489", "600547",
            
            # 化工
            "000301", "000422", "000792", "000830", "000983", "002007", "002129", "002236",
            
            # 石油石化
            "600028", "600688", "601857", "000301", "002648", "600346", "000792", "600409",
            
            # 煤炭
            "600188", "601088", "601225", "601666", "601898", "000983", "600123", "600395",
            
            # 电力及公用事业
            "000027", "000539", "000767", "000875", "600025", "600886", "601985", "000826",
            
            # 交通运输
            "000089", "600009", "600026", "600029", "600115", "600125", "600320", "600350",
            
            # 商贸零售
            "000069", "000501", "000759", "002024", "002120", "002251", "600361", "600682"
        ]
        
        # 去重并排序
        unique_stocks = sorted(list(set(hs300_core)))
        logger.info(f"Generated {len(unique_stocks)} HS300 stocks")
        
        return unique_stocks
    
    def get_data_tier(self, stock_code: str) -> int:
        """
        获取股票所属的数据层级
        
        Parameters:
        -----------
        stock_code : str
            股票代码
            
        Returns:
        --------
        int
            数据层级 (1-4)
        """
        if stock_code in self.tier_config['tier1']['stocks']:
            return 1
        elif stock_code in self.tier_config['tier2']['stocks']:
            return 2
        else:
            return 3
    
    def check_data_availability(self, assets: List[str], start_date: str, end_date: str) -> Dict:
        """
        检查数据可用性
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns:
        --------
        Dict
            数据可用性报告
        """
        self.stats['total_requests'] += 1
        
        report = {
            'total_assets': len(assets),
            'tier_distribution': {1: [], 2: [], 3: []},
            'data_status': {},
            'missing_data': [],
            'download_required': False,
            'estimated_download_time': 0
        }
        
        for asset in assets:
            tier = self.get_data_tier(asset)
            report['tier_distribution'][tier].append(asset)
            
            # 检查数据状态
            status = self._check_asset_data_status(asset, start_date, end_date)
            report['data_status'][asset] = status
            
            if not status['available']:
                report['missing_data'].append(asset)
        
        report['download_required'] = len(report['missing_data']) > 0
        report['estimated_download_time'] = len(report['missing_data']) * 2  # 每只股票约2秒
        
        return report
    
    def _check_asset_data_status(self, asset: str, start_date: str, end_date: str) -> Dict:
        """检查单个资产的数据状态"""
        try:
            # 检查数据库中的数据
            if hasattr(self.db_manage, 'quant_db'):
                # 首先检查哪些表存在
                available_tables = self._get_available_tables()

                # 优先检查日线数据表
                table_candidates = ['stock_daily', 'stock_1d', 'stock_day']
                table_to_use = None

                for table in table_candidates:
                    if table in available_tables:
                        table_to_use = table
                        break

                # 如果没有日线表，检查实时表作为备选
                if not table_to_use and 'stock_realtime' in available_tables:
                    # 对于实时表，我们假设数据不完整，但可用
                    return {
                        'available': True,  # 实时数据可用
                        'completeness': 0.1,  # 低完整性
                        'record_count': 1,  # 假设有一条记录
                        'date_range': (start_date, end_date),
                        'tier': self.get_data_tier(asset),
                        'data_source': 'realtime'
                    }

                if table_to_use:
                    query = f"""
                    SELECT COUNT(*) as count, MIN(date) as min_date, MAX(date) as max_date
                    FROM {table_to_use}
                    WHERE code = '{asset}'
                    AND date >= '{start_date}'
                    AND date <= '{end_date}'
                    """
                    result = self.db_manage.quant_db.conn.execute(query).fetchone()

                    if result and result[0] > 0:
                        # 计算数据完整性
                        expected_days = len(pd.bdate_range(start_date, end_date))
                        completeness = result[0] / expected_days if expected_days > 0 else 0

                        return {
                            'available': completeness >= 0.8,  # 80%以上认为可用
                            'completeness': completeness,
                            'record_count': result[0],
                            'date_range': (result[1], result[2]) if result[1] else None,
                            'tier': self.get_data_tier(asset),
                            'data_source': table_to_use
                        }

            return {
                'available': False,
                'completeness': 0,
                'record_count': 0,
                'date_range': None,
                'tier': self.get_data_tier(asset),
                'data_source': 'none'
            }

        except Exception as e:
            logger.warning(f"Error checking data status for {asset}: {e}")
            return {
                'available': False,
                'completeness': 0,
                'record_count': 0,
                'date_range': None,
                'tier': self.get_data_tier(asset),
                'error': str(e),
                'data_source': 'error'
            }

    def _get_available_tables(self) -> List[str]:
        """获取数据库中可用的表列表"""
        try:
            if hasattr(self.db_manage, 'quant_db'):
                # 查询所有表
                result = self.db_manage.quant_db.conn.execute("SHOW TABLES").fetchall()
                tables = [row[0] for row in result]
                return tables
        except Exception as e:
            logger.warning(f"Error getting available tables: {e}")

        return []
    
    def get_tiered_data(self, assets: List[str], start_date: str, end_date: str, 
                       fields: List[str] = None, auto_download: bool = True) -> pd.DataFrame:
        """
        分层获取数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段
        auto_download : bool, optional
            是否自动下载缺失数据
            
        Returns:
        --------
        pd.DataFrame
            股票数据，MultiIndex格式
        """
        if fields is None:
            fields = ['open', 'high', 'low', 'close', 'volume', 'amount']
        
        # 检查数据可用性
        availability = self.check_data_availability(assets, start_date, end_date)
        
        # 获取可用数据
        available_assets = [asset for asset, status in availability['data_status'].items() 
                          if status['available']]
        
        data_frames = []
        
        # 从数据库获取可用数据
        if available_assets:
            try:
                db_data = self._get_data_from_db(available_assets, start_date, end_date, fields)
                if not db_data.empty:
                    data_frames.append(db_data)
                    self.stats['cache_hits'] += len(available_assets)
            except Exception as e:
                logger.error(f"Error getting data from database: {e}")
        
        # 下载缺失数据
        missing_assets = availability['missing_data']
        if missing_assets and auto_download:
            try:
                downloaded_data = self._download_missing_data(missing_assets, start_date, end_date, fields)
                if not downloaded_data.empty:
                    data_frames.append(downloaded_data)
                    self.stats['downloads'] += len(missing_assets)
                    self.stats['cache_misses'] += len(missing_assets)
            except Exception as e:
                logger.error(f"Error downloading missing data: {e}")
        
        # 合并数据
        if data_frames:
            result = pd.concat(data_frames, axis=0)
            result = result.sort_index()
            return result
        else:
            # 返回空的MultiIndex DataFrame
            index = pd.MultiIndex.from_tuples([], names=['date', 'asset'])
            return pd.DataFrame(index=index, columns=fields)
    
    def _get_data_from_db(self, assets: List[str], start_date: str, end_date: str,
                         fields: List[str]) -> pd.DataFrame:
        """从数据库获取数据"""
        try:
            # 获取可用的表
            available_tables = self._get_available_tables()

            # 确定要使用的表
            table_candidates = ['stock_daily', 'stock_1d', 'stock_day']
            table_to_use = None

            for table in table_candidates:
                if table in available_tables:
                    table_to_use = table
                    break

            # 如果没有日线表，尝试使用实时表
            if not table_to_use and 'stock_realtime' in available_tables:
                logger.info("Using stock_realtime table as fallback")
                return self._get_data_from_realtime_table(assets, fields)

            if not table_to_use:
                logger.warning("No suitable data table found")
                return pd.DataFrame()

            # 构建查询
            assets_str = "', '".join(assets)

            # 检查表中实际存在的字段
            available_fields = self._get_table_columns(table_to_use)
            valid_fields = [f for f in fields if f in available_fields]

            if not valid_fields:
                logger.warning(f"No valid fields found in table {table_to_use}")
                return pd.DataFrame()

            # 确保包含必要的字段
            query_fields = valid_fields.copy()
            if 'date' in available_fields and 'date' not in query_fields:
                query_fields.append('date')
            if 'code' in available_fields and 'code' not in query_fields:
                query_fields.append('code')

            fields_str = ", ".join(query_fields)

            query = f"""
            SELECT {fields_str}
            FROM {table_to_use}
            WHERE code IN ('{assets_str}')
            AND date >= '{start_date}'
            AND date <= '{end_date}'
            ORDER BY date, code
            """

            df = self.db_manage.quant_db.conn.execute(query).df()

            if not df.empty and 'date' in df.columns and 'code' in df.columns:
                # 转换为MultiIndex格式
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index(['date', 'code'])
                df.index.names = ['date', 'asset']

                # 只保留需要的字段
                available_result_fields = [f for f in valid_fields if f in df.columns]
                if available_result_fields:
                    df = df[available_result_fields]

            return df

        except Exception as e:
            logger.error(f"Error querying database: {e}")
            return pd.DataFrame()

    def _get_data_from_realtime_table(self, assets: List[str], fields: List[str]) -> pd.DataFrame:
        """从实时表获取数据（作为备选方案）"""
        try:
            assets_str = "', '".join(assets)

            # 获取实时表的字段
            available_fields = self._get_table_columns('stock_realtime')
            valid_fields = [f for f in fields if f in available_fields]

            if not valid_fields:
                return pd.DataFrame()

            query_fields = valid_fields.copy()
            if 'code' in available_fields and 'code' not in query_fields:
                query_fields.append('code')

            fields_str = ", ".join(query_fields)

            query = f"""
            SELECT {fields_str}
            FROM stock_realtime
            WHERE code IN ('{assets_str}')
            """

            df = self.db_manage.quant_db.conn.execute(query).df()

            if not df.empty and 'code' in df.columns:
                # 为实时数据添加当前日期
                current_date = pd.Timestamp.now().normalize()
                df['date'] = current_date

                # 转换为MultiIndex格式
                df = df.set_index(['date', 'code'])
                df.index.names = ['date', 'asset']

                # 只保留需要的字段
                available_result_fields = [f for f in valid_fields if f in df.columns]
                if available_result_fields:
                    df = df[available_result_fields]

            return df

        except Exception as e:
            logger.error(f"Error querying realtime table: {e}")
            return pd.DataFrame()

    def _get_table_columns(self, table_name: str) -> List[str]:
        """获取表的列名"""
        try:
            if hasattr(self.db_manage, 'quant_db'):
                result = self.db_manage.quant_db.conn.execute(f"PRAGMA table_info('{table_name}')").fetchall()
                columns = [row[1] for row in result]  # 列名在第二个位置
                return columns
        except Exception as e:
            logger.warning(f"Error getting columns for table {table_name}: {e}")

        return []
    
    def _download_missing_data(self, assets: List[str], start_date: str, end_date: str, 
                              fields: List[str]) -> pd.DataFrame:
        """下载缺失数据"""
        logger.info(f"Downloading data for {len(assets)} assets: {assets}")
        
        data_frames = []
        
        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_asset = {
                executor.submit(self._download_single_asset, asset, start_date, end_date): asset
                for asset in assets
            }
            
            for future in as_completed(future_to_asset):
                asset = future_to_asset[future]
                try:
                    df = future.result()
                    if not df.empty:
                        data_frames.append(df)
                        # 保存到数据库
                        self._save_to_db(df, asset)
                except Exception as e:
                    logger.error(f"Error downloading data for {asset}: {e}")
        
        if data_frames:
            result = pd.concat(data_frames, axis=0)
            return result
        else:
            return pd.DataFrame()
    
    def _download_single_asset(self, asset: str, start_date: str, end_date: str) -> pd.DataFrame:
        """下载单个资产的数据"""
        try:
            # 使用data_engine下载数据
            df = self.data_engine._get_stock_hist_info(asset, "daily", "")
            
            if not df.empty:
                # 过滤日期范围
                df['date'] = pd.to_datetime(df['date'])
                mask = (df['date'] >= start_date) & (df['date'] <= end_date)
                df = df[mask]
                
                # 添加资产标识
                df['asset'] = asset
                
                # 设置MultiIndex
                df = df.set_index(['date', 'asset'])
                
            return df
            
        except Exception as e:
            logger.error(f"Error downloading data for {asset}: {e}")
            return pd.DataFrame()
    
    def _save_to_db(self, df: pd.DataFrame, asset: str):
        """保存数据到数据库"""
        try:
            # 重置索引以便保存
            df_to_save = df.reset_index()
            df_to_save['code'] = asset
            
            # 保存到数据库
            self.db_manage.quant_db.save_data("stock", "daily", df_to_save, if_exists="append")
            
        except Exception as e:
            logger.error(f"Error saving data to database for {asset}: {e}")
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        total_requests = self.stats['total_requests']
        if total_requests > 0:
            cache_hit_rate = self.stats['cache_hits'] / total_requests
        else:
            cache_hit_rate = 0
        
        return {
            'cache_hit_rate': cache_hit_rate,
            'total_requests': total_requests,
            'cache_hits': self.stats['cache_hits'],
            'cache_misses': self.stats['cache_misses'],
            'downloads': self.stats['downloads']
        }
    
    def preload_core_data(self, years: int = 3) -> Dict:
        """
        预加载核心股票池数据
        
        Parameters:
        -----------
        years : int
            预加载年数
            
        Returns:
        --------
        Dict
            预加载结果
        """
        core_stocks = self.tier_config['tier1']['stocks']
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=years * 365)).strftime('%Y-%m-%d')
        
        logger.info(f"Starting preload for {len(core_stocks)} core stocks")
        
        # 检查哪些数据需要下载
        availability = self.check_data_availability(core_stocks, start_date, end_date)
        missing_stocks = availability['missing_data']
        
        result = {
            'total_stocks': len(core_stocks),
            'already_available': len(core_stocks) - len(missing_stocks),
            'downloaded': 0,
            'failed': 0,
            'start_time': datetime.now(),
            'end_time': None,
            'duration': None
        }
        
        if missing_stocks:
            logger.info(f"Downloading data for {len(missing_stocks)} missing stocks")
            
            # 下载缺失数据
            try:
                downloaded_data = self._download_missing_data(
                    missing_stocks, start_date, end_date, 
                    ['open', 'high', 'low', 'close', 'volume', 'amount']
                )
                
                if not downloaded_data.empty:
                    result['downloaded'] = len(missing_stocks)
                else:
                    result['failed'] = len(missing_stocks)
                    
            except Exception as e:
                logger.error(f"Error during preload: {e}")
                result['failed'] = len(missing_stocks)
                result['error'] = str(e)
        
        result['end_time'] = datetime.now()
        result['duration'] = (result['end_time'] - result['start_time']).total_seconds()
        
        logger.info(f"Preload completed: {result}")
        return result
