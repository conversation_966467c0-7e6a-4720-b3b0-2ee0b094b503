"""
统一的因子研究框架

整合了FactorEngine和FactorAnalyzer的功能，提供一站式的因子研究解决方案。
遵循单一职责原则，简化架构，提高可维护性。

主要功能:
- 因子计算和管理
- 基于alphalens的因子分析
- 数据获取和缓存
- 可视化和报告生成
- 批量分析和比较
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Any, Type
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

logger = logging.getLogger(__name__)

# 尝试导入alphalens
try:
    import alphalens as al
    ALPHALENS_AVAILABLE = True
    logger.info("Alphalens imported successfully")
except ImportError:
    ALPHALENS_AVAILABLE = False
    logger.warning("Alphalens not available. Some analysis features will be limited.")


class FactorResearch:
    """
    统一的因子研究框架
    
    整合了因子计算、分析、可视化等功能，提供简洁统一的接口。
    """
    
    def __init__(self, db_manage, data_engine=None, cache_enabled: bool = True, 
                 parallel_enabled: bool = False, max_workers: int = 4):
        """
        初始化因子研究框架
        
        Parameters:
        -----------
        db_manage : DBManage
            数据库管理对象
        data_engine : DataEngine, optional
            数据引擎对象，如果不提供则自动创建
        cache_enabled : bool, optional
            是否启用缓存，默认True
        parallel_enabled : bool, optional
            是否启用并行计算，默认False
        max_workers : int, optional
            最大工作线程数，默认4
        """
        self.db_manage = db_manage
        
        # 获取或创建data_engine实例
        if data_engine is not None:
            self.data_engine = data_engine
        else:
            from engine.data_engine.data_engine import DataEngine
            self.data_engine = DataEngine(quant_db=db_manage.quant_db if db_manage else None)
        
        # 配置参数
        self.cache_enabled = cache_enabled
        self.parallel_enabled = parallel_enabled
        self.max_workers = max_workers
        
        # 内部状态
        self._cache = {}
        self._cache_stats = {'hits': 0, 'misses': 0}
        self._registered_factors = {}
        self._performance_stats = {}
        self._analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'simplified_analyses': 0
        }
        
        # 检查alphalens可用性
        self.alphalens_available = ALPHALENS_AVAILABLE
        if not self.alphalens_available:
            logger.warning("Alphalens not available. Using simplified analysis methods.")
        
        # 注册默认因子
        self._register_default_factors()
        
        logger.info(f"FactorResearch initialized with cache={cache_enabled}, parallel={parallel_enabled}")
    
    # ==================== 因子计算相关方法 ====================
    
    def register_factor(self, factor_class: Type, **default_params):
        """
        注册因子类
        
        Parameters:
        -----------
        factor_class : Type
            因子类
        **default_params : dict
            默认参数
        """
        try:
            factor_name = factor_class.__name__
            self._registered_factors[factor_name] = {
                'class': factor_class,
                'default_params': default_params
            }
            logger.info(f"Factor registered: {factor_name}")
        except Exception as e:
            logger.error(f"Error registering factor {factor_class.__name__}: {e}")
            raise
    
    def calculate_factor(self, factor_name: str, assets: List[str], 
                        start_date: str, end_date: str, **params) -> pd.Series:
        """
        计算单个因子
        
        Parameters:
        -----------
        factor_name : str
            因子名称
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        **params : dict
            因子计算参数
            
        Returns:
        --------
        pd.Series
            因子数据，MultiIndex格式
        """
        try:
            start_time = time.time()
            
            # 检查因子是否已注册
            if factor_name not in self._registered_factors:
                raise ValueError(f"Factor {factor_name} not registered")
            
            # 获取因子配置
            factor_config = self._registered_factors[factor_name]
            factor_class = factor_config['class']
            
            # 合并参数
            final_params = factor_config['default_params'].copy()
            final_params.update(params)
            
            # 获取价格数据
            price_data = self.get_price_data(assets, start_date, end_date)
            if price_data.empty:
                logger.warning(f"No price data available for factor calculation: {factor_name}")
                return pd.Series(dtype=float)
            
            # 实例化因子并计算
            factor_instance = factor_class(**final_params)
            factor_values = factor_instance.calculate(price_data)
            
            # 确保数据格式正确
            factor_values = self._ensure_factor_format(factor_values)
            factor_values.name = factor_name
            
            # 记录性能统计
            calculation_time = time.time() - start_time
            self._performance_stats[factor_name] = {
                'calculation_time': calculation_time,
                'data_points': len(factor_values),
                'assets_count': len(assets)
            }
            
            logger.info(f"Factor calculated: {factor_name}, time: {calculation_time:.2f}s, points: {len(factor_values)}")
            return factor_values
            
        except Exception as e:
            logger.error(f"Error calculating factor {factor_name}: {e}")
            raise
    
    def calculate_multiple_factors(self, factor_names: List[str], assets: List[str],
                                  start_date: str, end_date: str, **common_params) -> Dict[str, pd.Series]:
        """
        批量计算多个因子
        
        Parameters:
        -----------
        factor_names : List[str]
            因子名称列表
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        **common_params : dict
            公共参数
            
        Returns:
        --------
        Dict[str, pd.Series]
            因子数据字典
        """
        try:
            logger.info(f"Calculating {len(factor_names)} factors for {len(assets)} assets")
            
            if self.parallel_enabled and len(factor_names) > 1:
                return self._calculate_factors_parallel(factor_names, assets, start_date, end_date, **common_params)
            else:
                return self._calculate_factors_sequential(factor_names, assets, start_date, end_date, **common_params)
                
        except Exception as e:
            logger.error(f"Error in batch factor calculation: {e}")
            raise
    
    # ==================== 数据获取相关方法 ====================
    
    def get_price_data(self, assets: List[str], start_date: str, end_date: str, 
                      fields: List[str] = None) -> pd.DataFrame:
        """
        获取价格数据（已清洗，因子分析就绪）
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段，默认['close', 'open', 'high', 'low', 'volume']
            
        Returns:
        --------
        pd.DataFrame
            价格数据，MultiIndex格式
        """
        try:
            if fields is None:
                fields = ['close', 'open', 'high', 'low', 'volume']
            
            # 检查缓存
            cache_key = self._generate_cache_key('price', fields, start_date, end_date, assets)
            
            if self.cache_enabled and cache_key in self._cache:
                self._cache_stats['hits'] += 1
                logger.debug(f"Cache hit for price data: {len(assets)} assets")
                return self._cache[cache_key].copy()
            
            self._cache_stats['misses'] += 1
            
            # 从data_engine获取数据
            logger.debug(f"Fetching price data for {len(assets)} assets")
            data = self.data_engine.get_stock_data(
                symbols=assets,
                start_date=start_date,
                end_date=end_date,
                fields=fields
            )
            
            if data.empty:
                logger.warning("No price data found")
                return pd.DataFrame()
            
            # 使用data_engine进行因子分析专用清洗
            cleaned_data = self.data_engine.prepare_price_data_for_analysis(data)
            
            # 缓存结果
            if self.cache_enabled:
                self._update_cache(cache_key, cleaned_data)
            
            logger.debug(f"Retrieved and cleaned price data: {cleaned_data.shape}")
            return cleaned_data

        except Exception as e:
            logger.error(f"Error getting price data: {e}")
            raise

    # ==================== 因子分析相关方法 ====================

    def analyze_factor(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20],
                      quantiles: int = 5, max_loss: float = 0.35,
                      groupby_data: pd.Series = None) -> Dict[str, Any]:
        """
        完整的因子分析

        Parameters:
        -----------
        factor_data : pd.Series
            因子数据，MultiIndex (date, asset)，已清洗
        periods : List[int], optional
            持有期列表，默认 [1, 5, 10, 20]
        quantiles : int, optional
            分位数数量，默认5
        max_loss : float, optional
            最大数据丢失比例，默认0.35
        groupby_data : pd.Series, optional
            分组数据，如行业分类

        Returns:
        --------
        Dict[str, Any]
            分析结果字典
        """
        try:
            self._analysis_stats['total_analyses'] += 1
            start_time = time.time()

            logger.info(f"Starting factor analysis with {len(factor_data)} data points")

            if not self.alphalens_available:
                logger.warning("Alphalens not available, using simplified analysis")
                return self._simplified_factor_analysis(factor_data, periods)

            # 1. 获取价格数据
            assets = factor_data.index.get_level_values(1).unique().tolist()
            start_date = factor_data.index.get_level_values(0).min().strftime('%Y-%m-%d')
            end_date = factor_data.index.get_level_values(0).max().strftime('%Y-%m-%d')

            pricing_data = self.get_price_data(assets, start_date, end_date, fields=['close'])
            if pricing_data.empty:
                logger.error("No pricing data available for factor analysis")
                return self._simplified_factor_analysis(factor_data, periods)

            # 2. 转换为alphalens格式
            factor_data_alphalens = self._convert_to_alphalens_format(
                factor_data, pricing_data, periods, quantiles, max_loss
            )

            # 检查是否返回了简化分析结果
            if isinstance(factor_data_alphalens, dict):
                return factor_data_alphalens

            # 3. 执行各种分析
            results = {}

            # IC分析
            results.update(self._ic_analysis(factor_data_alphalens))

            # 分位数分析
            results.update(self._quantile_analysis(factor_data_alphalens))

            # 换手率分析
            results.update(self._turnover_analysis(factor_data_alphalens))

            # 因子收益分析
            results.update(self._factor_returns_analysis(factor_data_alphalens))

            # 分组分析（如果有分组数据）
            if groupby_data is not None:
                results.update(self._group_analysis(factor_data_alphalens, groupby_data))

            # 添加元数据
            results['metadata'] = {
                'analysis_time': time.time() - start_time,
                'data_points': len(factor_data),
                'assets_count': len(assets),
                'periods': periods,
                'quantiles': quantiles,
                'max_loss': max_loss,
                'alphalens_used': True
            }

            self._analysis_stats['successful_analyses'] += 1
            logger.info(f"Factor analysis completed in {results['metadata']['analysis_time']:.2f}s")
            return results

        except Exception as e:
            self._analysis_stats['failed_analyses'] += 1
            logger.error(f"Error in factor analysis: {e}")

            # 降级到简化分析
            logger.info("Falling back to simplified analysis")
            return self._simplified_factor_analysis(factor_data, periods)

    def compare_factors(self, factor_data_dict: Dict[str, pd.Series],
                       periods: List[int] = [1, 5, 10, 20]) -> Dict[str, Any]:
        """
        比较多个因子的表现

        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        periods : List[int], optional
            持有期列表，默认 [1, 5, 10, 20]

        Returns:
        --------
        Dict[str, Any]
            比较结果
        """
        try:
            logger.info(f"Comparing {len(factor_data_dict)} factors")

            comparison_results = {}
            for factor_name, factor_data in factor_data_dict.items():
                logger.info(f"Analyzing factor: {factor_name}")
                results = self.analyze_factor(factor_data, periods=periods)
                comparison_results[factor_name] = results

            # 生成比较摘要
            summary = self._generate_comparison_summary(comparison_results)

            return {
                'individual_results': comparison_results,
                'comparison_summary': summary,
                'metadata': {
                    'factors_count': len(factor_data_dict),
                    'periods': periods
                }
            }

        except Exception as e:
            logger.error(f"Error in factor comparison: {e}")
            raise

    # ==================== 工具方法 ====================

    def _ensure_factor_format(self, factor_values: pd.Series) -> pd.Series:
        """确保因子数据格式正确（MultiIndex）"""
        try:
            # 如果已经是MultiIndex，直接返回
            if isinstance(factor_values.index, pd.MultiIndex) and factor_values.index.nlevels == 2:
                logger.debug("Factor data already has correct MultiIndex format")
                return factor_values

            # 使用data_engine进行格式转换
            logger.debug("Converting factor data to MultiIndex format")
            cleaned_factor = self.data_engine.prepare_factor_data_for_analysis(factor_values)

            # 保持原有的因子名称
            cleaned_factor.name = factor_values.name

            logger.debug(f"Factor data converted to MultiIndex: {cleaned_factor.shape}")
            return cleaned_factor

        except Exception as e:
            logger.error(f"Error ensuring factor format: {e}")
            # 如果转换失败，返回原数据
            return factor_values

    def _generate_cache_key(self, data_type: str, fields: List[str], start_date: str,
                           end_date: str, assets: List[str]) -> str:
        """生成缓存键"""
        assets_str = '_'.join(sorted(assets))
        fields_str = '_'.join(sorted(fields))
        return f"{data_type}_{fields_str}_{start_date}_{end_date}_{hash(assets_str)}"

    def _update_cache(self, cache_key: str, data: pd.DataFrame):
        """更新缓存"""
        if len(self._cache) >= 100:  # 缓存大小限制
            # 删除最旧的缓存项
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]

        self._cache[cache_key] = data.copy()

    def _register_default_factors(self):
        """注册默认因子"""
        try:
            # 导入并注册默认因子
            from ..factors.momentum import MomentumFactor, RSIFactor, PriceVolumeTrendFactor

            # 注册动量因子
            self.register_factor(MomentumFactor, lookback_period=20, skip_period=1)

            # 注册RSI因子
            self.register_factor(RSIFactor, period=14)

            # 注册价量趋势因子
            self.register_factor(PriceVolumeTrendFactor, period=20)

            logger.info("Default factors registration completed")
        except Exception as e:
            logger.error(f"Error registering default factors: {str(e)}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'cache_stats': self._cache_stats,
            'performance_stats': self._performance_stats,
            'analysis_stats': self._analysis_stats,
            'registered_factors': list(self._registered_factors.keys()),
            'alphalens_available': self.alphalens_available
        }

    # ==================== 私有辅助方法 ====================

    def _calculate_factors_sequential(self, factor_names: List[str], assets: List[str],
                                    start_date: str, end_date: str, **common_params) -> Dict[str, pd.Series]:
        """顺序计算多个因子"""
        results = {}
        for factor_name in factor_names:
            try:
                factor_data = self.calculate_factor(factor_name, assets, start_date, end_date, **common_params)
                results[factor_name] = factor_data
            except Exception as e:
                logger.error(f"Error calculating factor {factor_name}: {e}")
                results[factor_name] = pd.Series(dtype=float)
        return results

    def _calculate_factors_parallel(self, factor_names: List[str], assets: List[str],
                                   start_date: str, end_date: str, **common_params) -> Dict[str, pd.Series]:
        """并行计算多个因子"""
        results = {}

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_factor = {
                executor.submit(self.calculate_factor, factor_name, assets, start_date, end_date, **common_params): factor_name
                for factor_name in factor_names
            }

            # 收集结果
            for future in as_completed(future_to_factor):
                factor_name = future_to_factor[future]
                try:
                    factor_data = future.result()
                    results[factor_name] = factor_data
                except Exception as e:
                    logger.error(f"Error calculating factor {factor_name}: {e}")
                    results[factor_name] = pd.Series(dtype=float)

        return results

    def _convert_to_alphalens_format(self, factor_data: pd.Series, pricing_data: pd.DataFrame,
                                   periods: List[int], quantiles: int, max_loss: float):
        """转换为alphalens格式"""
        try:
            if not self.alphalens_available:
                return self._simplified_factor_analysis(factor_data, periods)

            # 提取收盘价
            if 'close' in pricing_data.columns:
                prices = pricing_data['close'].unstack()
            else:
                prices = pricing_data.iloc[:, 0].unstack()  # 使用第一列

            # 使用alphalens进行数据格式转换
            factor_data_alphalens = al.utils.get_clean_factor_and_forward_returns(
                factor=factor_data,
                prices=prices,
                periods=periods,
                quantiles=quantiles,
                max_loss=max_loss
            )

            return factor_data_alphalens

        except Exception as e:
            logger.warning(f"Alphalens format conversion failed: {e}, using simplified analysis")
            self._analysis_stats['simplified_analyses'] += 1
            return self._simplified_factor_analysis(factor_data, periods)

    def _simplified_factor_analysis(self, factor_data: pd.Series, periods: List[int]) -> Dict[str, Any]:
        """简化的因子分析（当alphalens不可用时）"""
        try:
            logger.info("Performing simplified factor analysis")

            results = {
                'factor_stats': {
                    'mean': factor_data.mean(),
                    'std': factor_data.std(),
                    'skew': factor_data.skew(),
                    'kurt': factor_data.kurtosis(),
                    'count': len(factor_data),
                    'null_count': factor_data.isnull().sum()
                },
                'metadata': {
                    'analysis_type': 'simplified',
                    'alphalens_used': False,
                    'periods': periods
                }
            }

            # 计算分位数统计
            quantiles = [0.2, 0.4, 0.6, 0.8, 1.0]
            results['quantile_stats'] = factor_data.quantile(quantiles).to_dict()

            self._analysis_stats['simplified_analyses'] += 1
            return results

        except Exception as e:
            logger.error(f"Error in simplified analysis: {e}")
            return {'error': str(e), 'analysis_type': 'failed'}

    def _ic_analysis(self, factor_data_alphalens) -> Dict[str, Any]:
        """IC分析"""
        try:
            ic = al.performance.factor_information_coefficient(factor_data_alphalens)
            ic_summary = al.performance.factor_information_coefficient(factor_data_alphalens).describe()

            return {
                'ic_series': ic,
                'ic_summary': ic_summary,
                'ic_mean': ic.mean(),
                'ic_std': ic.std(),
                'ic_ir': ic.mean() / ic.std() if ic.std() != 0 else 0
            }
        except Exception as e:
            logger.warning(f"IC analysis failed: {e}")
            return {'ic_analysis_error': str(e)}

    def _quantile_analysis(self, factor_data_alphalens) -> Dict[str, Any]:
        """分位数分析"""
        try:
            quantile_returns = al.performance.mean_return_by_quantile(factor_data_alphalens)

            return {
                'quantile_returns': quantile_returns[0],  # 平均收益
                'quantile_returns_std': quantile_returns[1]  # 收益标准差
            }
        except Exception as e:
            logger.warning(f"Quantile analysis failed: {e}")
            return {'quantile_analysis_error': str(e)}

    def _turnover_analysis(self, factor_data_alphalens) -> Dict[str, Any]:
        """换手率分析"""
        try:
            turnover = al.performance.factor_rank_autocorrelation(factor_data_alphalens)

            return {
                'factor_autocorr': turnover
            }
        except Exception as e:
            logger.warning(f"Turnover analysis failed: {e}")
            return {'turnover_analysis_error': str(e)}

    def _factor_returns_analysis(self, factor_data_alphalens) -> Dict[str, Any]:
        """因子收益分析"""
        try:
            factor_returns = al.performance.factor_returns(factor_data_alphalens)

            return {
                'factor_returns': factor_returns
            }
        except Exception as e:
            logger.warning(f"Factor returns analysis failed: {e}")
            return {'factor_returns_error': str(e)}

    def _group_analysis(self, factor_data_alphalens, groupby_data: pd.Series) -> Dict[str, Any]:
        """分组分析"""
        try:
            # 这里需要根据具体需求实现分组分析
            # 暂时返回空结果
            return {'group_analysis': 'Not implemented yet'}
        except Exception as e:
            logger.warning(f"Group analysis failed: {e}")
            return {'group_analysis_error': str(e)}

    def _generate_comparison_summary(self, comparison_results: Dict[str, Dict]) -> Dict[str, Any]:
        """生成因子比较摘要"""
        try:
            summary = {}

            # 提取IC信息比率
            ic_ir_dict = {}
            for factor_name, results in comparison_results.items():
                if 'ic_ir' in results:
                    ic_ir_dict[factor_name] = results['ic_ir']

            if ic_ir_dict:
                summary['ic_ir_ranking'] = sorted(ic_ir_dict.items(), key=lambda x: x[1], reverse=True)
                summary['best_factor_by_ic_ir'] = max(ic_ir_dict.items(), key=lambda x: x[1])

            return summary

        except Exception as e:
            logger.warning(f"Error generating comparison summary: {e}")
            return {'summary_error': str(e)}


# 异常类定义
class FactorCalculationError(Exception):
    """因子计算异常"""
    pass


class FactorDataError(Exception):
    """因子数据异常"""
    pass


class FactorAnalysisError(Exception):
    """因子分析异常"""
    pass
